const { JsonRpc } = require('eosjs');
const fetch = (...args) =>
    import('node-fetch').then(({ default: fetch }) => fetch(...args));
const config = require('./config.json');
const { sendDingtalkMessage } = require('./dd.js');

// 超时工具函数
function withTimeout(promise, ms, errorMessage = '请求超时') {
    return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => reject(new Error(errorMessage)), ms);
        promise
            .then((result) => {
                clearTimeout(timeout);
                resolve(result);
            })
            .catch((error) => {
                clearTimeout(timeout);
                reject(error);
            });
    });
}

async function testSingleRpc(url, timeout = 5000) {
    const rpc = new JsonRpc(url, { fetch });

    try {
        const start = Date.now(); // 记录开始时间
        const info = await withTimeout(rpc.get_info(), timeout, `节点 ${rpc.endpoint} 超时`);
        const latency = Date.now() - start; // 计算延迟
        console.log(`节点 ${rpc.endpoint} 可用, 延迟: ${latency}ms, 信息: ${JSON.stringify(info)}`);
        return { rpc, latency }; // 返回节点和延迟信息
    } catch (error) {
        console.error(`节点 ${rpc.endpoint} 不可用: ${error.message}`);
        return null; // 返回 null 表示节点不可用
    }
}

// 每小时测试节点状态并发送通知
const rpcUrl = config.dfs_server_urls[0]; // 替换为你要测试的 RPC 节点 URL

setInterval(async () => {
    const result = await testSingleRpc(rpcUrl);
    if (result) {
        // 如果节点可用，发送可用通知
        const message = `节点 ${rpcUrl} 可用，延迟: ${result.latency}ms`;
        sendDingtalkMessage(config.nodebot, message); // 使用配置中的 webhook 发送通知
    } else {
        // 如果节点不可用，发送不可用通知
        const message = `节点 ${rpcUrl} 不可用！`;
        sendDingtalkMessage(config.nodebot, message); // 使用配置中的 webhook 发送通知
    }
}, 3600000); // 每小时执行一次（3600000 毫秒）

module.exports = { testSingleRpc }; // 如果需要导出函数
