#!/usr/bin/env node

const https = require('https');
const fs = require('fs');
const { sendDingtalkMessage } = require('./dd.js');
const config = require('./config.json');

// 项目数据存储文件
const PROJECTS_DATA_FILE = './ppp_projects_data.json';

// 简单的fetch实现，使用https模块
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = https.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          statusText: res.statusMessage,
          json: () => Promise.resolve(JSON.parse(data)),
          text: () => Promise.resolve(data)
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}



class PppFunProjectsAPI {
  constructor() {
    this.baseUrl = 'https://api.ppp.fun';
  }

  // 获取项目列表
  async getProjects(options = {}) {
    const {
      limit = 20,
      page = 1,
      keyword = '',
      sort = ''
    } = options;

    try {
      const url = `${this.baseUrl}/projects?limit=${limit}&page=${page}&keyword=${encodeURIComponent(keyword)}&sort=${sort}`;
      console.log(`正在请求: ${url}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'PPP-Fun-Projects-Tool/1.0.0'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error('API 返回失败状态');
      }

      return data;
    } catch (error) {
      console.error(`请求失败: ${error.message}`);
      throw error;
    }
  }

  // 提取项目基本信息
  extractProjectInfo(project) {
    return {
      // 基本标识
      project_pubkey: project.project_pubkey,
      mint_pubkey: project.mint_pubkey,
      project_mint: project.project_mint,

      // 项目信息
      project_name: project.project_name,
      project_desc: project.project_desc,
      token_symbol: project.token_symbol,

      // 代币信息
      init_token_supply: project.init_token_supply,
      max_token_supply: project.max_token_supply,
      circulation_supply: project.circulation_supply,

      // NFT 信息
      nft_name: project.nft_name,
      nft_id: project.nft_id,
      nft_issue_count: project.nft_issue_count,
      nft_burn_count: project.nft_burn_count,

      // 市场数据
      market_cap: project.market_cap,
      fdv: project.fdv,
      volume_24h: project.volume_24h,
      tx_24h: project.tx_24h,

      // 媒体资源
      image_url: project.image_url,
      nft_image_url: project.nft_image_url,
      token_uri: project.token_uri,

      // 社交链接
      website: project.website,
      twitter: project.twitter,
      telegram: project.telegram,
      discord: project.discord,

      // 时间信息
      create_time: project.create_time,
      updated_at: project.updated_at,

      // 创建者信息
      creator_pubkey: project.creator_pubkey,
      authority_pubkey: project.authority_pubkey
    };
  }

  // 显示项目信息
  displayProject(project, index) {
    const info = this.extractProjectInfo(project);

    console.log(`\n📊 项目 ${index + 1}: ${info.project_name}`);
    console.log('═'.repeat(60));

    console.log('🏷️  基本信息:');
    console.log(`项目名称: ${info.project_name}`);
    console.log(`项目描述: ${info.project_desc}`);
    console.log(`代币符号: ${info.token_symbol}`);
    console.log(`NFT 名称: ${info.nft_name}`);

    console.log('\n🔑 合约地址:');
    console.log(`项目地址: ${info.project_pubkey}`);
    console.log(`代币地址: ${info.mint_pubkey}`);
    console.log(`创建者: ${info.creator_pubkey}`);

    console.log('\n💰 代币信息:');
    console.log(`初始供应量: ${this.formatNumber(info.init_token_supply)}`);
    console.log(`最大供应量: ${this.formatNumber(info.max_token_supply)}`);
    console.log(`流通供应量: ${this.formatNumber(info.circulation_supply)}`);

    console.log('\n🎨 NFT 信息:');
    console.log(`NFT ID: ${info.nft_id}`);
    console.log(`已发行: ${info.nft_issue_count}`);
    console.log(`已销毁: ${info.nft_burn_count}`);

    console.log('\n📈 市场数据:');
    console.log(`市值: $${this.formatNumber(info.market_cap)}`);
    console.log(`FDV: $${this.formatNumber(info.fdv)}`);
    console.log(`24h 交易量: $${this.formatNumber(info.volume_24h)}`);
    console.log(`24h 交易次数: ${info.tx_24h}`);

    if (info.website || info.twitter || info.telegram || info.discord) {
      console.log('\n🔗 社交链接:');
      if (info.website) console.log(`官网: ${info.website}`);
      if (info.twitter) console.log(`Twitter: ${info.twitter}`);
      if (info.telegram) console.log(`Telegram: ${info.telegram}`);
      if (info.discord) console.log(`Discord: ${info.discord}`);
    }

    console.log('\n🖼️  媒体资源:');
    if (info.image_url) console.log(`项目图片: ${info.image_url}`);
    if (info.nft_image_url) console.log(`NFT 图片: ${info.nft_image_url}`);

    console.log('\n⏰ 时间信息:');
    console.log(`创建时间: ${new Date(info.create_time).toLocaleString()}`);
    console.log(`更新时间: ${new Date(info.updated_at).toLocaleString()}`);
  }

  // 格式化数字
  formatNumber(num) {
    if (!num) return '0';
    return new Intl.NumberFormat().format(num);
  }

  // 保存项目数据
  saveProjectsData(projects, filename) {
    try {
      const extractedData = projects.map(project => this.extractProjectInfo(project));
      fs.writeFileSync(filename, JSON.stringify(extractedData, null, 2));
      console.log(`✅ 数据已保存到: ${filename}`);
      return filename;
    } catch (error) {
      console.error(`保存失败: ${error.message}`);
      return null;
    }
  }

  // 保存为 CSV 格式
  saveProjectsCSV(projects, filename) {
    try {
      const extractedData = projects.map(project => this.extractProjectInfo(project));

      if (extractedData.length === 0) {
        throw new Error('没有数据可保存');
      }

      // CSV 头部
      const headers = Object.keys(extractedData[0]);
      const csvContent = [
        headers.join(','),
        ...extractedData.map(row =>
          headers.map(header => `"${row[header] || ''}"`).join(',')
        )
      ].join('\n');

      fs.writeFileSync(filename, csvContent);
      console.log(`✅ CSV 数据已保存到: ${filename}`);
      return filename;
    } catch (error) {
      console.error(`保存 CSV 失败: ${error.message}`);
      return null;
    }
  }

  // 搜索项目
  async searchProjects(keyword, limit = 20) {
    console.log(`正在搜索项目: "${keyword}"`);
    return await this.getProjects({ keyword, limit });
  }

  // 获取热门项目（按交易量排序）
  async getHotProjects(limit = 20) {
    console.log('正在获取热门项目...');
    return await this.getProjects({ limit, sort: 'volume_24h_desc' });
  }

  // 获取最新项目
  async getLatestProjects(limit = 20) {
    console.log('正在获取最新项目...');
    // sendDingtalkMessage(config.newnftbot, '开始获取最新项目...');
    return await this.getProjects({ limit, sort: '' });
  }

  // 加载已保存的项目数据
  loadSavedProjects() {
    try {
      if (fs.existsSync(PROJECTS_DATA_FILE)) {
        const data = fs.readFileSync(PROJECTS_DATA_FILE, 'utf8');
        return JSON.parse(data);
      }
      return [];
    } catch (error) {
      console.error(`加载项目数据失败: ${error.message}`);
      return [];
    }
  }

  // 保存项目数据到文件
  saveProjectsToFile(projects) {
    try {
      const extractedData = projects.map(project => this.extractProjectInfo(project));
      fs.writeFileSync(PROJECTS_DATA_FILE, JSON.stringify(extractedData, null, 2));
      console.log(`✅ 项目数据已保存到: ${PROJECTS_DATA_FILE}`);
      return true;
    } catch (error) {
      console.error(`保存项目数据失败: ${error.message}`);
      return false;
    }
  }

  // 检查新项目
  checkForNewProjects(currentProjects, savedProjects) {
    const savedProjectIds = new Set(savedProjects.map(p => p.project_pubkey));
    const newProjects = currentProjects.filter(project =>
      !savedProjectIds.has(project.project_pubkey)
    );
    return newProjects;
  }

  // 格式化新项目消息
  formatNewProjectMessage(project) {
    const info = this.extractProjectInfo(project);
    return `🎉 发现新项目！
📊 项目名称: ${info.project_name}
🏷️ 代币符号: ${info.token_symbol}
📝 项目描述: ${info.project_desc}
📝 CA: ${info.project_mint}
💰 市值: $${this.formatNumber(info.market_cap)}
📈 24h交易量: $${this.formatNumber(info.volume_24h)}
🔗 项目地址: ${info.project_pubkey}
⏰ 创建时间: ${new Date(info.create_time).toLocaleString()}`;
  }

  // 监控新项目
  async monitorNewProjects() {
    try {
      console.log('🔍 正在检查新项目...');

      // 获取当前最新项目
      const result = await this.getLatestProjects(50); // 获取更多项目以确保不遗漏

      if (!result || !result.data || result.data.length === 0) {
        console.log('未获取到项目数据');
        return;
      }

      // 加载已保存的项目数据
      const savedProjects = this.loadSavedProjects();

      if (savedProjects.length === 0) {
        // 第一次运行，保存当前项目数据
        console.log('📝 首次运行，保存当前项目数据...');
        this.saveProjectsToFile(result.data);
        console.log(`✅ 已保存 ${result.data.length} 个项目的数据`);
        return;
      }

      // 检查新项目
      const newProjects = this.checkForNewProjects(result.data, savedProjects);

      if (newProjects.length > 0) {
        console.log(`🎉 发现 ${newProjects.length} 个新项目！`);

        // 发送钉钉通知
        for (const project of newProjects) {
          const message = this.formatNewProjectMessage(project);
          console.log('发送钉钉通知...');
          console.log(message);

          try {
            await sendDingtalkMessage(config.newnftbot, message);
            console.log('✅ 钉钉消息发送成功');
          } catch (error) {
            console.error(`钉钉消息发送失败: ${error.message}`);
          }
        }

        // 更新保存的项目数据
        this.saveProjectsToFile(result.data);
      } else {
        console.log('未发现新项目');
      }

    } catch (error) {
      console.error(`监控新项目时出错: ${error.message}`);
    }
  }

  // 启动监控模式
  startMonitoring(intervalSeconds = 10) {
    console.log(`🚀 启动项目监控模式，每 ${intervalSeconds} 秒检查一次新项目\n`);

    // 立即执行一次
    this.monitorNewProjects();

    // 设置定时器
    const interval = setInterval(() => {
      this.monitorNewProjects();
    }, intervalSeconds * 1000);

    // 优雅退出处理
    process.on('SIGINT', () => {
      console.log('\n🛑 收到退出信号，正在停止监控...');
      clearInterval(interval);
      console.log('✅ 监控已停止');
      process.exit(0);
    });

    return interval;
  }
}

// 主函数
async function main() {
  console.log('🎯 PPP.Fun 项目监控工具\n');

  const api = new PppFunProjectsAPI();

  // 直接启动监控模式
  console.log('🔄 启动新项目监控 (每10秒检查一次)');
  api.startMonitoring(10);
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.log(`❌ 未捕获的异常: ${error.message}`);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.log(`❌ 未处理的Promise拒绝: ${reason}`);
  process.exit(1);
});

// 启动
main().catch(console.error);
