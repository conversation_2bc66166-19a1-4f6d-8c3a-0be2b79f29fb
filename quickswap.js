// 导入 DfsWallet 实例
const dfsWallet = require('./dfs.js'); // 确保路径正确
const config = require('./config.json');
const swapWithMid = require('./swapmid.js');
const { sendDingtalkMessage } = require('./dd.js');
const { logToFile } = require('./logger.js');

(async () => {

    // 定义表状态
    const tableStates = {
        projects: {
            lastIds: new Set(), // 用于存储已查看的 row.id
            firstRun: true, // 标记是否为首次运行
            issending: true, // 是否可以发送消息
        },
    };



    // 创建 DfsWallet 实例
    const myDfsWallet = new dfsWallet();
    // 初始化钱包
    await myDfsWallet.init('MyApp', config.account.private_key);
    const delay = 5000; // 延迟时间（毫秒）
    // 定义轮询逻辑
    const checkAndTransfer = async () => {
        try {
            console.log('Checking for new projects...');
            let table = 'projects'; // 假设默认检查 projects 表       
            const tableState = tableStates[table]; // 获取当前表的状态
            if (!tableState) {
                console.error(`未找到表状态: ${table}`);
                return;
            }

            logToFile(`检查 ${table} 更新...`);

            const postsData = await myDfsWallet.getTableRows1("dfs3protocol", "dfs3protocol", table, {
                json: true,
                limit: 1,
                index_position: 1,
                reverse: true,
                show_payer: false,
                key_type: 'i64',
            });
            let mid;
            if (postsData && postsData.rows) {
                logToFile(`Found ${postsData.rows.length}  ${table}`);
                postsData.rows.forEach(async row => {
                    // 检查 row.id 是否已经存在
                    if (!tableState.lastIds.has(row.id)) {
                        tableState.lastIds.add(row.id);	
                        logToFile(` ${table} found project ID: ${row.id}`);
                        logToFile(` ${table} found new project : ${row.project_name}`);
                        mid = row.mid;
                        logToFile(` ${table} found mid : ${mid}`);
//          await swapWithMid(myDfsWallet, mid, 0.01, 110);
                    if (tableState.firstRun) {
                            tableState.firstRun = false; // 修改标志为非首次运行
                        } else {   // 否则发送通知                          
                            sendDingtalkMessage(config.swapbot2, "新图 发射成功! 项目名称: " + row.project_name)
                            // 调用 swapWithMid 函数
                            await swapWithMid(myDfsWallet, mid, 5, 1);
                        }
                    }
                })
            }
        } catch (error) {
            console.error('Error retrieving data:', error);
        }
    };
    // 轮询函数
    setInterval(checkAndTransfer, delay);
})();
