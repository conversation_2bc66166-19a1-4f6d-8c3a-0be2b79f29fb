/*! pako 2.0.3 https://github.com/nodeca/pako @license (MIT AND Zlib) */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).pako={})}(this,(function(e){"use strict";var t=function(e,t,i,a){for(var n=65535&e|0,r=e>>>16&65535|0,o=0;0!==i;){i-=o=i>2e3?2e3:i;do{r=r+(n=n+t[a++]|0)|0}while(--o);n%=65521,r%=65521}return n|r<<16|0},i=new Uint32Array(function(){for(var e,t=[],i=0;i<256;i++){e=i;for(var a=0;a<8;a++)e=1&e?**********^e>>>1:e>>>1;t[i]=e}return t}()),a=function(e,t,a,n){var r=i,o=n+a;e^=-1;for(var s=n;s<o;s++)e=e>>>8^r[255&(e^t[s])];return-1^e},n=function(e,t){var i,a,n,r,o,s,l,f,d,h,c,u,w,b,m,k,_,v,g,p,y,x,E,R,A=e.state;i=e.next_in,E=e.input,a=i+(e.avail_in-5),n=e.next_out,R=e.output,r=n-(t-e.avail_out),o=n+(e.avail_out-257),s=A.dmax,l=A.wsize,f=A.whave,d=A.wnext,h=A.window,c=A.hold,u=A.bits,w=A.lencode,b=A.distcode,m=(1<<A.lenbits)-1,k=(1<<A.distbits)-1;e:do{u<15&&(c+=E[i++]<<u,u+=8,c+=E[i++]<<u,u+=8),_=w[c&m];t:for(;;){if(c>>>=v=_>>>24,u-=v,0===(v=_>>>16&255))R[n++]=65535&_;else{if(!(16&v)){if(0==(64&v)){_=w[(65535&_)+(c&(1<<v)-1)];continue t}if(32&v){A.mode=12;break e}e.msg="invalid literal/length code",A.mode=30;break e}g=65535&_,(v&=15)&&(u<v&&(c+=E[i++]<<u,u+=8),g+=c&(1<<v)-1,c>>>=v,u-=v),u<15&&(c+=E[i++]<<u,u+=8,c+=E[i++]<<u,u+=8),_=b[c&k];i:for(;;){if(c>>>=v=_>>>24,u-=v,!(16&(v=_>>>16&255))){if(0==(64&v)){_=b[(65535&_)+(c&(1<<v)-1)];continue i}e.msg="invalid distance code",A.mode=30;break e}if(p=65535&_,u<(v&=15)&&(c+=E[i++]<<u,(u+=8)<v&&(c+=E[i++]<<u,u+=8)),(p+=c&(1<<v)-1)>s){e.msg="invalid distance too far back",A.mode=30;break e}if(c>>>=v,u-=v,p>(v=n-r)){if((v=p-v)>f&&A.sane){e.msg="invalid distance too far back",A.mode=30;break e}if(y=0,x=h,0===d){if(y+=l-v,v<g){g-=v;do{R[n++]=h[y++]}while(--v);y=n-p,x=R}}else if(d<v){if(y+=l+d-v,(v-=d)<g){g-=v;do{R[n++]=h[y++]}while(--v);if(y=0,d<g){g-=v=d;do{R[n++]=h[y++]}while(--v);y=n-p,x=R}}}else if(y+=d-v,v<g){g-=v;do{R[n++]=h[y++]}while(--v);y=n-p,x=R}for(;g>2;)R[n++]=x[y++],R[n++]=x[y++],R[n++]=x[y++],g-=3;g&&(R[n++]=x[y++],g>1&&(R[n++]=x[y++]))}else{y=n-p;do{R[n++]=R[y++],R[n++]=R[y++],R[n++]=R[y++],g-=3}while(g>2);g&&(R[n++]=R[y++],g>1&&(R[n++]=R[y++]))}break}}break}}while(i<a&&n<o);i-=g=u>>3,c&=(1<<(u-=g<<3))-1,e.next_in=i,e.next_out=n,e.avail_in=i<a?a-i+5:5-(i-a),e.avail_out=n<o?o-n+257:257-(n-o),A.hold=c,A.bits=u},r=15,o=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),s=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),l=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),f=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]),d=function(e,t,i,a,n,d,h,c){var u,w,b,m,k,_,v,g,p,y=c.bits,x=0,E=0,R=0,A=0,Z=0,S=0,O=0,U=0,T=0,D=0,I=null,B=0,N=new Uint16Array(16),C=new Uint16Array(16),z=null,F=0;for(x=0;x<=r;x++)N[x]=0;for(E=0;E<a;E++)N[t[i+E]]++;for(Z=y,A=r;A>=1&&0===N[A];A--);if(Z>A&&(Z=A),0===A)return n[d++]=20971520,n[d++]=20971520,c.bits=1,0;for(R=1;R<A&&0===N[R];R++);for(Z<R&&(Z=R),U=1,x=1;x<=r;x++)if(U<<=1,(U-=N[x])<0)return-1;if(U>0&&(0===e||1!==A))return-1;for(C[1]=0,x=1;x<r;x++)C[x+1]=C[x]+N[x];for(E=0;E<a;E++)0!==t[i+E]&&(h[C[t[i+E]]++]=E);if(0===e?(I=z=h,_=19):1===e?(I=o,B-=257,z=s,F-=257,_=256):(I=l,z=f,_=-1),D=0,E=0,x=R,k=d,S=Z,O=0,b=-1,m=(T=1<<Z)-1,1===e&&T>852||2===e&&T>592)return 1;for(;;){v=x-O,h[E]<_?(g=0,p=h[E]):h[E]>_?(g=z[F+h[E]],p=I[B+h[E]]):(g=96,p=0),u=1<<x-O,R=w=1<<S;do{n[k+(D>>O)+(w-=u)]=v<<24|g<<16|p|0}while(0!==w);for(u=1<<x-1;D&u;)u>>=1;if(0!==u?(D&=u-1,D+=u):D=0,E++,0==--N[x]){if(x===A)break;x=t[i+h[E]]}if(x>Z&&(D&m)!==b){for(0===O&&(O=Z),k+=R,U=1<<(S=x-O);S+O<A&&!((U-=N[S+O])<=0);)S++,U<<=1;if(T+=1<<S,1===e&&T>852||2===e&&T>592)return 1;n[b=D&m]=Z<<24|S<<16|k-d|0}}return 0!==D&&(n[k+D]=x-O<<24|64<<16|0),c.bits=Z,0},h={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8},c=h.Z_FINISH,u=h.Z_BLOCK,w=h.Z_TREES,b=h.Z_OK,m=h.Z_STREAM_END,k=h.Z_NEED_DICT,_=h.Z_STREAM_ERROR,v=h.Z_DATA_ERROR,g=h.Z_MEM_ERROR,p=h.Z_BUF_ERROR,y=h.Z_DEFLATED,x=12,E=30,R=function(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)};function A(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Uint16Array(320),this.work=new Uint16Array(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}var Z,S,O=function(e){if(!e||!e.state)return _;var t=e.state;return e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=1,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new Int32Array(852),t.distcode=t.distdyn=new Int32Array(592),t.sane=1,t.back=-1,b},U=function(e){if(!e||!e.state)return _;var t=e.state;return t.wsize=0,t.whave=0,t.wnext=0,O(e)},T=function(e,t){var i;if(!e||!e.state)return _;var a=e.state;return t<0?(i=0,t=-t):(i=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?_:(null!==a.window&&a.wbits!==t&&(a.window=null),a.wrap=i,a.wbits=t,U(e))},D=function(e,t){if(!e)return _;var i=new A;e.state=i,i.window=null;var a=T(e,t);return a!==b&&(e.state=null),a},I=!0,B=function(e){if(I){Z=new Int32Array(512),S=new Int32Array(32);for(var t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(d(1,e.lens,0,288,Z,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;d(2,e.lens,0,32,S,0,e.work,{bits:5}),I=!1}e.lencode=Z,e.lenbits=9,e.distcode=S,e.distbits=5},N=function(e,t,i,a){var n,r=e.state;return null===r.window&&(r.wsize=1<<r.wbits,r.wnext=0,r.whave=0,r.window=new Uint8Array(r.wsize)),a>=r.wsize?(r.window.set(t.subarray(i-r.wsize,i),0),r.wnext=0,r.whave=r.wsize):((n=r.wsize-r.wnext)>a&&(n=a),r.window.set(t.subarray(i-a,i-a+n),r.wnext),(a-=n)?(r.window.set(t.subarray(i-a,i),0),r.wnext=a,r.whave=r.wsize):(r.wnext+=n,r.wnext===r.wsize&&(r.wnext=0),r.whave<r.wsize&&(r.whave+=n))),0},C={inflateReset:U,inflateReset2:T,inflateResetKeep:O,inflateInit:function(e){return D(e,15)},inflateInit2:D,inflate:function(e,i){var r,o,s,l,f,h,A,Z,S,O,U,T,D,I,C,z,F,L,M,H,j,K,P,Y,G=0,X=new Uint8Array(4),W=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return _;(r=e.state).mode===x&&(r.mode=13),f=e.next_out,s=e.output,A=e.avail_out,l=e.next_in,o=e.input,h=e.avail_in,Z=r.hold,S=r.bits,O=h,U=A,K=b;e:for(;;)switch(r.mode){case 1:if(0===r.wrap){r.mode=13;break}for(;S<16;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}if(2&r.wrap&&35615===Z){r.check=0,X[0]=255&Z,X[1]=Z>>>8&255,r.check=a(r.check,X,2,0),Z=0,S=0,r.mode=2;break}if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&Z)<<8)+(Z>>8))%31){e.msg="incorrect header check",r.mode=E;break}if((15&Z)!==y){e.msg="unknown compression method",r.mode=E;break}if(S-=4,j=8+(15&(Z>>>=4)),0===r.wbits)r.wbits=j;else if(j>r.wbits){e.msg="invalid window size",r.mode=E;break}r.dmax=1<<r.wbits,e.adler=r.check=1,r.mode=512&Z?10:x,Z=0,S=0;break;case 2:for(;S<16;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}if(r.flags=Z,(255&r.flags)!==y){e.msg="unknown compression method",r.mode=E;break}if(57344&r.flags){e.msg="unknown header flags set",r.mode=E;break}r.head&&(r.head.text=Z>>8&1),512&r.flags&&(X[0]=255&Z,X[1]=Z>>>8&255,r.check=a(r.check,X,2,0)),Z=0,S=0,r.mode=3;case 3:for(;S<32;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}r.head&&(r.head.time=Z),512&r.flags&&(X[0]=255&Z,X[1]=Z>>>8&255,X[2]=Z>>>16&255,X[3]=Z>>>24&255,r.check=a(r.check,X,4,0)),Z=0,S=0,r.mode=4;case 4:for(;S<16;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}r.head&&(r.head.xflags=255&Z,r.head.os=Z>>8),512&r.flags&&(X[0]=255&Z,X[1]=Z>>>8&255,r.check=a(r.check,X,2,0)),Z=0,S=0,r.mode=5;case 5:if(1024&r.flags){for(;S<16;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}r.length=Z,r.head&&(r.head.extra_len=Z),512&r.flags&&(X[0]=255&Z,X[1]=Z>>>8&255,r.check=a(r.check,X,2,0)),Z=0,S=0}else r.head&&(r.head.extra=null);r.mode=6;case 6:if(1024&r.flags&&((T=r.length)>h&&(T=h),T&&(r.head&&(j=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Uint8Array(r.head.extra_len)),r.head.extra.set(o.subarray(l,l+T),j)),512&r.flags&&(r.check=a(r.check,o,T,l)),h-=T,l+=T,r.length-=T),r.length))break e;r.length=0,r.mode=7;case 7:if(2048&r.flags){if(0===h)break e;T=0;do{j=o[l+T++],r.head&&j&&r.length<65536&&(r.head.name+=String.fromCharCode(j))}while(j&&T<h);if(512&r.flags&&(r.check=a(r.check,o,T,l)),h-=T,l+=T,j)break e}else r.head&&(r.head.name=null);r.length=0,r.mode=8;case 8:if(4096&r.flags){if(0===h)break e;T=0;do{j=o[l+T++],r.head&&j&&r.length<65536&&(r.head.comment+=String.fromCharCode(j))}while(j&&T<h);if(512&r.flags&&(r.check=a(r.check,o,T,l)),h-=T,l+=T,j)break e}else r.head&&(r.head.comment=null);r.mode=9;case 9:if(512&r.flags){for(;S<16;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}if(Z!==(65535&r.check)){e.msg="header crc mismatch",r.mode=E;break}Z=0,S=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),e.adler=r.check=0,r.mode=x;break;case 10:for(;S<32;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}e.adler=r.check=R(Z),Z=0,S=0,r.mode=11;case 11:if(0===r.havedict)return e.next_out=f,e.avail_out=A,e.next_in=l,e.avail_in=h,r.hold=Z,r.bits=S,k;e.adler=r.check=1,r.mode=x;case x:if(i===u||i===w)break e;case 13:if(r.last){Z>>>=7&S,S-=7&S,r.mode=27;break}for(;S<3;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}switch(r.last=1&Z,S-=1,3&(Z>>>=1)){case 0:r.mode=14;break;case 1:if(B(r),r.mode=20,i===w){Z>>>=2,S-=2;break e}break;case 2:r.mode=17;break;case 3:e.msg="invalid block type",r.mode=E}Z>>>=2,S-=2;break;case 14:for(Z>>>=7&S,S-=7&S;S<32;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}if((65535&Z)!=(Z>>>16^65535)){e.msg="invalid stored block lengths",r.mode=E;break}if(r.length=65535&Z,Z=0,S=0,r.mode=15,i===w)break e;case 15:r.mode=16;case 16:if(T=r.length){if(T>h&&(T=h),T>A&&(T=A),0===T)break e;s.set(o.subarray(l,l+T),f),h-=T,l+=T,A-=T,f+=T,r.length-=T;break}r.mode=x;break;case 17:for(;S<14;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}if(r.nlen=257+(31&Z),Z>>>=5,S-=5,r.ndist=1+(31&Z),Z>>>=5,S-=5,r.ncode=4+(15&Z),Z>>>=4,S-=4,r.nlen>286||r.ndist>30){e.msg="too many length or distance symbols",r.mode=E;break}r.have=0,r.mode=18;case 18:for(;r.have<r.ncode;){for(;S<3;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}r.lens[W[r.have++]]=7&Z,Z>>>=3,S-=3}for(;r.have<19;)r.lens[W[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,P={bits:r.lenbits},K=d(0,r.lens,0,19,r.lencode,0,r.work,P),r.lenbits=P.bits,K){e.msg="invalid code lengths set",r.mode=E;break}r.have=0,r.mode=19;case 19:for(;r.have<r.nlen+r.ndist;){for(;z=(G=r.lencode[Z&(1<<r.lenbits)-1])>>>16&255,F=65535&G,!((C=G>>>24)<=S);){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}if(F<16)Z>>>=C,S-=C,r.lens[r.have++]=F;else{if(16===F){for(Y=C+2;S<Y;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}if(Z>>>=C,S-=C,0===r.have){e.msg="invalid bit length repeat",r.mode=E;break}j=r.lens[r.have-1],T=3+(3&Z),Z>>>=2,S-=2}else if(17===F){for(Y=C+3;S<Y;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}S-=C,j=0,T=3+(7&(Z>>>=C)),Z>>>=3,S-=3}else{for(Y=C+7;S<Y;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}S-=C,j=0,T=11+(127&(Z>>>=C)),Z>>>=7,S-=7}if(r.have+T>r.nlen+r.ndist){e.msg="invalid bit length repeat",r.mode=E;break}for(;T--;)r.lens[r.have++]=j}}if(r.mode===E)break;if(0===r.lens[256]){e.msg="invalid code -- missing end-of-block",r.mode=E;break}if(r.lenbits=9,P={bits:r.lenbits},K=d(1,r.lens,0,r.nlen,r.lencode,0,r.work,P),r.lenbits=P.bits,K){e.msg="invalid literal/lengths set",r.mode=E;break}if(r.distbits=6,r.distcode=r.distdyn,P={bits:r.distbits},K=d(2,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,P),r.distbits=P.bits,K){e.msg="invalid distances set",r.mode=E;break}if(r.mode=20,i===w)break e;case 20:r.mode=21;case 21:if(h>=6&&A>=258){e.next_out=f,e.avail_out=A,e.next_in=l,e.avail_in=h,r.hold=Z,r.bits=S,n(e,U),f=e.next_out,s=e.output,A=e.avail_out,l=e.next_in,o=e.input,h=e.avail_in,Z=r.hold,S=r.bits,r.mode===x&&(r.back=-1);break}for(r.back=0;z=(G=r.lencode[Z&(1<<r.lenbits)-1])>>>16&255,F=65535&G,!((C=G>>>24)<=S);){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}if(z&&0==(240&z)){for(L=C,M=z,H=F;z=(G=r.lencode[H+((Z&(1<<L+M)-1)>>L)])>>>16&255,F=65535&G,!(L+(C=G>>>24)<=S);){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}Z>>>=L,S-=L,r.back+=L}if(Z>>>=C,S-=C,r.back+=C,r.length=F,0===z){r.mode=26;break}if(32&z){r.back=-1,r.mode=x;break}if(64&z){e.msg="invalid literal/length code",r.mode=E;break}r.extra=15&z,r.mode=22;case 22:if(r.extra){for(Y=r.extra;S<Y;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}r.length+=Z&(1<<r.extra)-1,Z>>>=r.extra,S-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=23;case 23:for(;z=(G=r.distcode[Z&(1<<r.distbits)-1])>>>16&255,F=65535&G,!((C=G>>>24)<=S);){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}if(0==(240&z)){for(L=C,M=z,H=F;z=(G=r.distcode[H+((Z&(1<<L+M)-1)>>L)])>>>16&255,F=65535&G,!(L+(C=G>>>24)<=S);){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}Z>>>=L,S-=L,r.back+=L}if(Z>>>=C,S-=C,r.back+=C,64&z){e.msg="invalid distance code",r.mode=E;break}r.offset=F,r.extra=15&z,r.mode=24;case 24:if(r.extra){for(Y=r.extra;S<Y;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}r.offset+=Z&(1<<r.extra)-1,Z>>>=r.extra,S-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){e.msg="invalid distance too far back",r.mode=E;break}r.mode=25;case 25:if(0===A)break e;if(T=U-A,r.offset>T){if((T=r.offset-T)>r.whave&&r.sane){e.msg="invalid distance too far back",r.mode=E;break}T>r.wnext?(T-=r.wnext,D=r.wsize-T):D=r.wnext-T,T>r.length&&(T=r.length),I=r.window}else I=s,D=f-r.offset,T=r.length;T>A&&(T=A),A-=T,r.length-=T;do{s[f++]=I[D++]}while(--T);0===r.length&&(r.mode=21);break;case 26:if(0===A)break e;s[f++]=r.length,A--,r.mode=21;break;case 27:if(r.wrap){for(;S<32;){if(0===h)break e;h--,Z|=o[l++]<<S,S+=8}if(U-=A,e.total_out+=U,r.total+=U,U&&(e.adler=r.check=r.flags?a(r.check,s,U,f-U):t(r.check,s,U,f-U)),U=A,(r.flags?Z:R(Z))!==r.check){e.msg="incorrect data check",r.mode=E;break}Z=0,S=0}r.mode=28;case 28:if(r.wrap&&r.flags){for(;S<32;){if(0===h)break e;h--,Z+=o[l++]<<S,S+=8}if(Z!==(4294967295&r.total)){e.msg="incorrect length check",r.mode=E;break}Z=0,S=0}r.mode=29;case 29:K=m;break e;case E:K=v;break e;case 31:return g;case 32:default:return _}return e.next_out=f,e.avail_out=A,e.next_in=l,e.avail_in=h,r.hold=Z,r.bits=S,(r.wsize||U!==e.avail_out&&r.mode<E&&(r.mode<27||i!==c))&&N(e,e.output,e.next_out,U-e.avail_out),O-=e.avail_in,U-=e.avail_out,e.total_in+=O,e.total_out+=U,r.total+=U,r.wrap&&U&&(e.adler=r.check=r.flags?a(r.check,s,U,e.next_out-U):t(r.check,s,U,e.next_out-U)),e.data_type=r.bits+(r.last?64:0)+(r.mode===x?128:0)+(20===r.mode||15===r.mode?256:0),(0===O&&0===U||i===c)&&K===b&&(K=p),K},inflateEnd:function(e){if(!e||!e.state)return _;var t=e.state;return t.window&&(t.window=null),e.state=null,b},inflateGetHeader:function(e,t){if(!e||!e.state)return _;var i=e.state;return 0==(2&i.wrap)?_:(i.head=t,t.done=!1,b)},inflateSetDictionary:function(e,i){var a,n=i.length;return e&&e.state?0!==(a=e.state).wrap&&11!==a.mode?_:11===a.mode&&t(1,i,n,0)!==a.check?v:N(e,i,n,n)?(a.mode=31,g):(a.havedict=1,b):_},inflateInfo:"pako inflate (from Nodeca project)"};function z(e){return(z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var F=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},L=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var i=t.shift();if(i){if("object"!==z(i))throw new TypeError(i+"must be non-object");for(var a in i)F(i,a)&&(e[a]=i[a])}}return e},M=function(e){for(var t=0,i=0,a=e.length;i<a;i++)t+=e[i].length;for(var n=new Uint8Array(t),r=0,o=0,s=e.length;r<s;r++){var l=e[r];n.set(l,o),o+=l.length}return n},H=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){H=!1}for(var j=new Uint8Array(256),K=0;K<256;K++)j[K]=K>=252?6:K>=248?5:K>=240?4:K>=224?3:K>=192?2:1;j[254]=j[254]=1;var P=function(e){var t,i,a,n,r,o=e.length,s=0;for(n=0;n<o;n++)55296==(64512&(i=e.charCodeAt(n)))&&n+1<o&&56320==(64512&(a=e.charCodeAt(n+1)))&&(i=65536+(i-55296<<10)+(a-56320),n++),s+=i<128?1:i<2048?2:i<65536?3:4;for(t=new Uint8Array(s),r=0,n=0;r<s;n++)55296==(64512&(i=e.charCodeAt(n)))&&n+1<o&&56320==(64512&(a=e.charCodeAt(n+1)))&&(i=65536+(i-55296<<10)+(a-56320),n++),i<128?t[r++]=i:i<2048?(t[r++]=192|i>>>6,t[r++]=128|63&i):i<65536?(t[r++]=224|i>>>12,t[r++]=128|i>>>6&63,t[r++]=128|63&i):(t[r++]=240|i>>>18,t[r++]=128|i>>>12&63,t[r++]=128|i>>>6&63,t[r++]=128|63&i);return t},Y=function(e,t){var i,a,n=t||e.length,r=new Array(2*n);for(a=0,i=0;i<n;){var o=e[i++];if(o<128)r[a++]=o;else{var s=j[o];if(s>4)r[a++]=65533,i+=s-1;else{for(o&=2===s?31:3===s?15:7;s>1&&i<n;)o=o<<6|63&e[i++],s--;s>1?r[a++]=65533:o<65536?r[a++]=o:(o-=65536,r[a++]=55296|o>>10&1023,r[a++]=56320|1023&o)}}}return function(e,t){if(t<65534&&e.subarray&&H)return String.fromCharCode.apply(null,e.length===t?e:e.subarray(0,t));for(var i="",a=0;a<t;a++)i+=String.fromCharCode(e[a]);return i}(r,a)},G=function(e,t){(t=t||e.length)>e.length&&(t=e.length);for(var i=t-1;i>=0&&128==(192&e[i]);)i--;return i<0||0===i?t:i+j[e[i]]>t?i:t},X={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"};var W=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0};var q=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1},J=Object.prototype.toString,Q=h.Z_NO_FLUSH,V=h.Z_FINISH,$=h.Z_OK,ee=h.Z_STREAM_END,te=h.Z_NEED_DICT,ie=h.Z_STREAM_ERROR,ae=h.Z_DATA_ERROR,ne=h.Z_MEM_ERROR;function re(e){this.options=L({chunkSize:65536,windowBits:15,to:""},e||{});var t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&0==(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new W,this.strm.avail_out=0;var i=C.inflateInit2(this.strm,t.windowBits);if(i!==$)throw new Error(X[i]);if(this.header=new q,C.inflateGetHeader(this.strm,this.header),t.dictionary&&("string"==typeof t.dictionary?t.dictionary=P(t.dictionary):"[object ArrayBuffer]"===J.call(t.dictionary)&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw&&(i=C.inflateSetDictionary(this.strm,t.dictionary))!==$))throw new Error(X[i])}function oe(e,t){var i=new re(t);if(i.push(e),i.err)throw i.msg||X[i.err];return i.result}re.prototype.push=function(e,t){var i,a,n,r=this.strm,o=this.options.chunkSize,s=this.options.dictionary;if(this.ended)return!1;for(a=t===~~t?t:!0===t?V:Q,"[object ArrayBuffer]"===J.call(e)?r.input=new Uint8Array(e):r.input=e,r.next_in=0,r.avail_in=r.input.length;;){for(0===r.avail_out&&(r.output=new Uint8Array(o),r.next_out=0,r.avail_out=o),(i=C.inflate(r,a))===te&&s&&((i=C.inflateSetDictionary(r,s))===$?i=C.inflate(r,a):i===ae&&(i=te));r.avail_in>0&&i===ee&&r.state.wrap>0&&0!==e[r.next_in];)C.inflateReset(r),i=C.inflate(r,a);switch(i){case ie:case ae:case te:case ne:return this.onEnd(i),this.ended=!0,!1}if(n=r.avail_out,r.next_out&&(0===r.avail_out||i===ee))if("string"===this.options.to){var l=G(r.output,r.next_out),f=r.next_out-l,d=Y(r.output,l);r.next_out=f,r.avail_out=o-f,f&&r.output.set(r.output.subarray(l,l+f),0),this.onData(d)}else this.onData(r.output.length===r.next_out?r.output:r.output.subarray(0,r.next_out));if(i!==$||0!==n){if(i===ee)return i=C.inflateEnd(this.strm),this.onEnd(i),this.ended=!0,!0;if(0===r.avail_in)break}}return!0},re.prototype.onData=function(e){this.chunks.push(e)},re.prototype.onEnd=function(e){e===$&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=M(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var se=re,le=oe,fe=function(e,t){return(t=t||{}).raw=!0,oe(e,t)},de=oe,he=h,ce={Inflate:se,inflate:le,inflateRaw:fe,ungzip:de,constants:he};e.Inflate=se,e.constants=he,e.default=ce,e.inflate=le,e.inflateRaw=fe,e.ungzip=de,Object.defineProperty(e,"__esModule",{value:!0})}));
