{"name": "brorand", "version": "1.1.0", "description": "Random number generator for browsers and node.js", "main": "index.js", "scripts": {"test": "mocha --reporter=spec test/**/*-test.js"}, "repository": {"type": "git", "url": "**************:indutny/brorand"}, "keywords": ["Random", "RNG", "browser", "crypto"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/indutny/brorand/issues"}, "homepage": "https://github.com/indutny/brorand", "devDependencies": {"mocha": "^2.0.1"}, "browser": {"crypto": false}}