{"version": 3, "file": "PrivateKey.js", "sourceRoot": "", "sources": ["../src/PrivateKey.ts"], "names": [], "mappings": ";;;AACA,iDAMyB;AACzB,iEAAkF;AAElF,+FAA+F;AAC/F;IACI,oBAAoB,GAAQ,EAAU,EAAM;QAAxB,QAAG,GAAH,GAAG,CAAK;QAAU,OAAE,GAAF,EAAE,CAAI;IAAG,CAAC;IAEhD,oEAAoE;IACtD,uBAAY,GAA1B,UAA2B,OAAmB,EAAE,OAAgB,EAAE,EAAO;QACrE,IAAI,CAAC,EAAE,EAAE;YACL,EAAE,GAAG,yCAAiB,CAAC,OAAO,CAAC,CAAC;SACnC;QACD,OAAO,IAAI,UAAU,CAAC;YAClB,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;SAC3D,EAAE,EAAE,CAAC,CAAC;IACX,CAAC;IAED,+DAA+D;IACjD,qBAAU,GAAxB,UAAyB,SAAiB,EAAE,EAAO;QAC/C,IAAM,UAAU,GAAG,kCAAkB,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC,EAAE,EAAE;YACL,EAAE,GAAG,yCAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAC3C;QACD,OAAO,IAAI,UAAU,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED,0DAA0D;IACnD,+BAAU,GAAjB;QACI,OAAO,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAEM,mCAAc,GAArB;QACI,OAAO,wCAAwB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED,qDAAqD;IAC9C,6BAAQ,GAAf;QACI,OAAO,kCAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACxC,CAAC;IAED,4BAA4B;IACrB,4BAAO,GAAd;QACI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;IACzB,CAAC;IAED,iDAAiD;IAC1C,iCAAY,GAAnB;QACI,IAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAC7C,OAAO,iCAAS,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IAC/E,CAAC;IAED,+DAA+D;IACxD,yBAAI,GAAX,UAAY,IAAa,EAAE,UAA0B,EAAE,QAAiC;QAAxF,iBA0BC;QA1B0B,2BAAA,EAAA,iBAA0B;QAAE,yBAAA,EAAA,iBAAiC;QACpF,IAAI,UAAU,EAAE;YACZ,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC1B,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aACtC;YACD,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;SAC/C;QACD,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,SAAoB,CAAC;QACzB,IAAM,WAAW,GAAG,UAAC,OAAmB;YACpC,OAAA,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;mBAChE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;QADzE,CACyE,CAAC;QAC9E,IAAM,kBAAkB,GAAG,UAAC,OAAuB;YAC/C,IAAM,kBAAkB,GAAG,KAAI,CAAC,UAAU,EAAE,CAAC;YAC7C,IAAM,iBAAiB,GAAG,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACjE,OAAO,iCAAS,CAAC,YAAY,CAAC,iBAAiB,EAAE,KAAI,CAAC,OAAO,EAAE,EAAE,KAAI,CAAC,EAAE,CAAC,CAAC;QAC9E,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,uBAAO,CAAC,EAAE,EAAE;YAC9B,GAAG;gBACC,SAAS,GAAG,kBAAkB,CAAC,EAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAC,CAAC,CAAC;aACtE,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,EAAE;SAChD;aAAM;YACH,SAAS,GAAG,kBAAkB,CAAC,EAAC,SAAS,EAAE,IAAI,EAAC,CAAC,CAAC;SACrD;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,6BAA6B;IACtB,4BAAO,GAAd;QACI,IAAI;YACA,IAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAC7C,IAAM,aAAa,GAAG,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YACpD,OAAO,aAAa,CAAC,MAAM,CAAC;SAC/B;QAAC,WAAM;YACJ,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IACL,iBAAC;AAAD,CAAC,AAvFD,IAuFC;AAvFY,gCAAU"}