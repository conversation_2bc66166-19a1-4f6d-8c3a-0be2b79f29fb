{"version": 3, "file": "Signature.js", "sourceRoot": "", "sources": ["../src/Signature.ts"], "names": [], "mappings": ";;;AACA,0BAA6B;AAE7B,iDAKyB;AACzB,iEAAuE;AAEvE,6FAA6F;AAC7F;IACI,mBAAoB,SAAc,EAAU,EAAM;QAA9B,cAAS,GAAT,SAAS,CAAK;QAAU,OAAE,GAAF,EAAE,CAAI;IAAG,CAAC;IAEtD,2DAA2D;IAC7C,oBAAU,GAAxB,UAAyB,GAAW,EAAE,EAAO;QACzC,IAAM,SAAS,GAAG,iCAAiB,CAAC,GAAG,CAAC,CAAC;QACzC,IAAI,CAAC,EAAE,EAAE;YACL,EAAE,GAAG,yCAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;SAC1C;QACD,OAAO,IAAI,SAAS,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;IACxC,CAAC;IAED,gEAAgE;IAClD,sBAAY,GAA1B,UAA2B,WAAyB,EAAE,OAAgB,EAAE,EAAO;QAC3E,IAAM,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAC1C,IAAM,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAC1C,IAAI,kBAAkB,CAAC;QACvB,IAAI,OAAO,KAAK,uBAAO,CAAC,EAAE,IAAI,OAAO,KAAK,uBAAO,CAAC,EAAE,EAAE;YAClD,kBAAkB,GAAG,WAAW,CAAC,aAAa,GAAG,EAAE,CAAC;YACpD,IAAI,WAAW,CAAC,aAAa,IAAI,CAAC,EAAE;gBAChC,kBAAkB,IAAI,CAAC,CAAC;aAC3B;SACJ;aAAM,IAAI,OAAO,KAAK,uBAAO,CAAC,EAAE,EAAE;YAC/B,kBAAkB,GAAG,WAAW,CAAC,aAAa,CAAC;SAClD;QACD,IAAM,OAAO,GAAG,IAAI,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,EAAE,EAAE;YACL,EAAE,GAAG,yCAAiB,CAAC,OAAO,CAAC,CAAC;SACnC;QACD,OAAO,IAAI,SAAS,CAAC;YACjB,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,OAAO;SAChB,EAAE,EAAE,CAAC,CAAC;IACX,CAAC;IAED;;;;;OAKG;IACI,8BAAU,GAAjB;QACI,IAAM,SAAS,GAAG,EAAE,CAAC;QACrB,IAAM,SAAS,GAAG,EAAE,CAAC;QACrB,IAAM,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9D,IAAM,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;QAEtF,IAAI,wBAAwB,CAAC;QAC7B,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,uBAAO,CAAC,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,uBAAO,CAAC,EAAE,EAAE;YAC1E,wBAAwB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACvD,IAAI,wBAAwB,GAAG,CAAC,EAAE;gBAC9B,wBAAwB,IAAI,CAAC,CAAC;aACjC;SACJ;aAAM,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,uBAAO,CAAC,EAAE,EAAE;YAC3C,wBAAwB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACrD;QACD,IAAM,aAAa,GAAG,wBAAwB,GAAG,CAAC,CAAC;QACnD,OAAO,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,aAAa,eAAA,EAAE,CAAC;IACnC,CAAC;IAED,iDAAiD;IAC1C,4BAAQ,GAAf;QACI,OAAO,iCAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7C,CAAC;IAED,wCAAwC;IACjC,4BAAQ,GAAf;QACI,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IAC/B,CAAC;IAED,kCAAkC;IAC3B,2BAAO,GAAd;QACI,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IAC/B,CAAC;IAED,gFAAgF;IACzE,0BAAM,GAAb,UAAc,IAAa,EAAE,SAAoB,EAAE,UAA0B,EAAE,QAAiC;QAA7D,2BAAA,EAAA,iBAA0B;QAAE,yBAAA,EAAA,iBAAiC;QAC5G,IAAI,UAAU,EAAE;YACZ,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC1B,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aACtC;YACD,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;SAC/C;QACD,IAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAC5C,IAAM,iBAAiB,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;QACjD,OAAO,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC;IAChF,CAAC;IAED,iFAAiF;IAC1E,2BAAO,GAAd,UAAe,IAAa,EAAE,UAA0B,EAAE,QAAiC;QAA7D,2BAAA,EAAA,iBAA0B;QAAE,yBAAA,EAAA,iBAAiC;QACvF,IAAI,UAAU,EAAE;YACZ,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC1B,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aACtC;YACD,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;SAC/C;QACD,IAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAC5C,IAAM,kBAAkB,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAC5C,IAAI,EACJ,iBAAiB,EACjB,iBAAiB,CAAC,aAAa,EAC/B,QAAQ,CACX,CAAC;QACF,IAAM,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;QAC/D,OAAO,iCAAS,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IACzE,CAAC;IACL,gBAAC;AAAD,CAAC,AA1GD,IA0GC;AA1GY,8BAAS"}