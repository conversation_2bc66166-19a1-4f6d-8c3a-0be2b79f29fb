{"version": 3, "file": "eosjs-ecc-migration.js", "sourceRoot": "", "sources": ["../src/eosjs-ecc-migration.ts"], "names": [], "mappings": ";;;AAAA,6CAA+D;AAC/D,iEAAwD;AACxD,iDAAwC;AAG3B,QAAA,GAAG,GAAG;IACf,UAAU,EAAE,cAAY,OAAA,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAlC,CAAkC;IAC1D,eAAe,EAAE,cAAY,OAAA,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAlC,CAAkC;IAC/D,SAAS,EAAE,UACP,cAAuB,EAAE,OAAuE;QAAvE,wBAAA,EAAA,YAAuE;QAEhG,IAAI,cAAc,KAAK,SAAS,EAAE;YAC9B,OAAO,CAAC,IAAI,CAAC,2CAA2C;gBACpD,kCAAkC,CAAC,CAAC;SAC3C;QAEO,IAAA,UAAU,GAAK,uCAAe,CAAC,uBAAO,CAAC,EAAE,EAAE,OAAO,CAAC,WAAzC,CAA0C;QAC5D,OAAO,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC;IACxD,CAAC;IACD,WAAW,EAAE,cAAY,OAAA,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAlC,CAAkC;IAC3D,eAAe,EAAE,UAAC,GAAW,EAAE,aAAsB;QACjD,IAAI,aAAa,KAAK,SAAS,EAAE;YAC7B,OAAO,CAAC,IAAI,CAAC,0CAA0C;gBACnD,0DAA0D,CAAC,CAAC;SACnE;QAED,IAAM,UAAU,GAAG,wBAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAM,SAAS,GAAG,UAAU,CAAC,YAAY,EAAE,CAAC;QAC5C,OAAO,SAAS,CAAC,cAAc,EAAE,CAAC;IACtC,CAAC;IACD,aAAa,EAAE,UAAC,MAAc,EAAE,aAAsB;QAClD,IAAI,aAAa,KAAK,SAAS,EAAE;YAC7B,OAAO,CAAC,IAAI,CAAC,0CAA0C;gBACnD,0DAA0D,CAAC,CAAC;SACnE;QAED,IAAI;YACA,IAAM,SAAS,GAAG,uBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAC/C,OAAO,SAAS,CAAC,OAAO,EAAE,CAAC;SAC9B;QAAC,WAAM;YACJ,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IACD,cAAc,EAAE,UAAC,GAAW;QACxB,IAAI;YACA,IAAM,UAAU,GAAG,wBAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC9C,OAAO,UAAU,CAAC,OAAO,EAAE,CAAC;SAC/B;QAAC,WAAM;YACJ,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IACD,IAAI,EAAE,UAAC,IAAmB,EAAE,UAA6B,EAAE,QAAiC;QAAjC,yBAAA,EAAA,iBAAiC;QACxF,IAAM,OAAO,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,wBAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QAChG,IAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QACrD,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;IAChC,CAAC;IACD,QAAQ,EAAE,UAAC,UAAyB,EAAE,UAA6B,EAAE,QAAgC;QAAhC,yBAAA,EAAA,gBAAgC;QACjG,IAAM,OAAO,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,wBAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QAChG,IAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC5D,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;IAChC,CAAC;IACD,MAAM,EAAE,UACJ,SAAiB,EAAE,IAAY,EAAE,MAAwB,EAAE,QAAiC,EAAE,QAAwB;QAA3D,yBAAA,EAAA,iBAAiC;QAAE,yBAAA,EAAA,eAAwB;QAEtH,IAAM,SAAS,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,uBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACrF,IAAM,GAAG,GAAG,uBAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC5C,OAAO,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC3D,CAAC;IACD,OAAO,EAAE,UAAC,SAAiB,EAAE,IAAY,EAAE,QAAiC;QAAjC,yBAAA,EAAA,iBAAiC;QACxE,IAAM,GAAG,GAAG,uBAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QACpD,OAAO,SAAS,CAAC,cAAc,EAAE,CAAC;IACtC,CAAC;IACD,WAAW,EAAE,UAAC,SAAiB,EAAE,UAAyB,EAAE,QAAgC;QAAhC,yBAAA,EAAA,gBAAgC;QACxF,IAAM,GAAG,GAAG,uBAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC3D,OAAO,SAAS,CAAC,cAAc,EAAE,CAAC;IACtC,CAAC;IACD,MAAM,EAAE,UAAC,IAAmB,EAAE,cAAuB,EAAE,QAAiB;QACpE,IAAI,QAAQ,KAAK,SAAS,EAAE;YACxB,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;SACrD;QACD,IAAI,cAAc,KAAK,SAAS,EAAE;YAC9B,OAAO,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;SAC3D;QAED,OAAO,OAAO,CAAC,yBAAyB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC3D,CAAC;CACJ,CAAC"}