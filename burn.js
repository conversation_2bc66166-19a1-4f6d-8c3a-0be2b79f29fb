
const dfsWallet = require('./dfs'); // 确保路径正确
const config = require('./config.json');
const { json } = require('express');
const { selectOptimalRpc } = require('./rpc');
const { logToFile } = require('./logger');


(async () => {
    let cf = config.account1;
    console.log(cf.address);
    const myDfsWallet = new dfsWallet();
    await myDfsWallet.init('MyApp', cf.private_key);
    const account = cf.address; // 你的账户名
    const opts = {
        useFreeCpu: true,
        blocksBehind: 3,
        expireSeconds: 3600
    };
    const go = async () => {
        try {
            // 获取项目列表 获取自己地址里面的 NFT
            const tableRows = await myDfsWallet.getTableRows1('dfs3protocol', 'dfs3protocol', "registry", {
                json: true,
                limit: 800,
                index_position: 'tertiary',//tertiary
                reverse: true,
                show_payer: false,
                key_type: 'name',
                lower_bound: account,
                upper_bound: account,
            });
            let filteredRows = tableRows.rows.filter(row => row.pid === 161); // guild id 109


            if (filteredRows.length === 0) {
                console.log('No NFT found for this account');
                return;
            }

            for (let i = 0; i < filteredRows.length; i++) {
                const row = filteredRows[i];
                console.log(row.owner, row.id); // 打印出所有项目的 owner 和 id
                burn(row.id); // 调用 burn 函数
            }
        }
        catch (error) {
            console.error(error);
        }
    }
    const burn = async (id) => {
        // 销毁资产
        try {
            let data = myDfsWallet.assetidtohex(id);
            logToFile(`Starting Burn operation...${id} hex:${data}`)
            const transaction = {
                actions: [
                    {
                        account: 'dfs3protocol',
                        name: 'burn',
                        authorization: [{
                            actor: account,
                            permission: 'active',
                        }],
                        data: data
                    },
                ],
            };

            // console.log('Performing transaction:', transaction);
            const result = await myDfsWallet.transact(transaction, opts);
            console.log('Transaction result:', result);
            console.log('Burn successful');
        } catch (error) {
            console.error('Transaction error:', error.message.message);
        }
    };
    setInterval(go, 200);

})();
