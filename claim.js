const fs = require('fs'); // 引入文件系统模块
const dfsWallet = require('./dfs'); // 确保路径正确
const config = require('./config.json');
const { logToFile } = require('./logger');
const { log } = require('console');
const STATE_FILE = 'claim_state.json'; // 存储收矿时间和次数的文件名

// 初始化状态变量
let claimCount = 0;
let lastClaimTime = null;

// 加载之前的状态
try {
    if (fs.existsSync(STATE_FILE)) {
        const savedState = JSON.parse(fs.readFileSync(STATE_FILE, 'utf8'));
        claimCount = savedState.count || 0;
        lastClaimTime = savedState.lastClaimTime || null;
        logToFile(`Loaded previous state: count=${claimCount}, lastClaimTime=${lastClaimTime}`);
    }
} catch (error) {
    logToFile(`Error loading state: ${error.message}`);
}

// 初始化
(async () => {
    const myDfsWallet = new dfsWallet();
    await myDfsWallet.init('MyApp', config.account3.private_key);
    const account = config.account3.address;

    const opts = {
        useFreeCpu: true,
        blocksBehind: 3,
        expireSeconds: 3600
    };


    const checkBalanceAndClaim = async () => {
        const now = new Date(); // 添加当前时间
        logToFile('Starting balance check and claim...');
        const poolBalance = await myDfsWallet.getbalance('eosio.token', 'dfsbpsvoters', 'DFS');
        // console.log(analysis*1.05);
        if (parseFloat(poolBalance) >3) { 
            const data = myDfsWallet.nameToUint64(account);
            const transaction = {
                actions: [
                    {
                        account: 'dfsbpsvoters',
                        name: 'claim',
                        authorization: [{
                            actor: account,
                            permission: 'owner',
                        }],
                        data: data.bigEndian
                    },
                ],
            };

            try {
                logToFile('Performing transaction:', transaction);
                const result = await myDfsWallet.transact(transaction, opts);
                // logToFile('Transaction result:', result);
                logToFile(`Claim successful. Claimed ${JSON.stringify(result)}.`);

                // 提取 result 里面的 quantity 字段
                let claimedQuantity = null;
                try {
                    // 从交易结果中提取quantity信息
                    if (result && result.processed && result.processed.action_traces) {
                        for (const trace of result.processed.action_traces) {
                            if (trace.inline_traces) {
                                for (const inlineTrace of trace.inline_traces) {
                                    if (inlineTrace.act && inlineTrace.act.data && inlineTrace.act.data.quantity) {
                                        claimedQuantity = inlineTrace.act.data.quantity;
                                        break;
                                    }
                                }
                            }
                            if (claimedQuantity) break;
                        }
                    }

                    // 如果没有在action_traces中找到，尝试在其他位置查找
                    if (!claimedQuantity && result) {
                        const resultStr = JSON.stringify(result);
                        const quantityMatch = resultStr.match(/"quantity":\s*"([^"]+)"/);
                        if (quantityMatch) {
                            claimedQuantity = quantityMatch[1];
                        }
                    }

                    if (claimedQuantity) {
                        logToFile(`Successfully extracted claimed quantity: ${claimedQuantity}`);
                        console.log(`✅ 成功提取到收矿数量: ${claimedQuantity}`);
                    } else {
                        logToFile('Could not extract quantity from transaction result');
                        console.log('⚠️ 无法从交易结果中提取quantity字段');
                    }
                } catch (extractError) {
                    logToFile(`Error extracting quantity: ${extractError.message}`);
                    console.error('提取quantity时出错:', extractError.message);
                }

                claimCount++;
                lastClaimTime = now.toLocaleString();
                const state = {
                    lastClaimTime: now.toLocaleString(),
                    count: claimCount,
                    lastClaimedQuantity: claimedQuantity // 保存最后一次收矿的数量
                };
                fs.writeFileSync(STATE_FILE, JSON.stringify(state));
                logToFile(`Claim state updated: ${JSON.stringify(state)}`);
            } catch (error) {
                console.error('Transaction error:', error);
            }
        } else {
            logToFile('Balance is insufficient or does not meet threshold.');
        }

    };
    setInterval(checkBalanceAndClaim, 30 * 1000);
})();