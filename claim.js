const fs = require('fs'); // 引入文件系统模块
const dfsWallet = require('./dfs'); // 确保路径正确
const config = require('./config.json');
const { logToFile } = require('./logger');
const { log } = require('console');
const STATE_FILE = 'claim_state.json'; // 存储收矿时间和次数的文件名

// 初始化
(async () => {
    const myDfsWallet = new dfsWallet();
    await myDfsWallet.init('MyApp', config.account3.private_key);
    const account = config.account3.address;

    const opts = {
        useFreeCpu: true,
        blocksBehind: 3,
        expireSeconds: 3600
    };


    const checkBalanceAndClaim = async () => {
        logToFile('Starting balance check and claim...');
        const poolBalance = await myDfsWallet.getbalance('eosio.token', 'dfsbpsvoters', 'DFS');
        // console.log(analysis*1.05);
        if (parseFloat(poolBalance) >3) { 
            const data = myDfsWallet.nameToUint64(account);
            const transaction = {
                actions: [
                    {
                        account: 'dfsbpsvoters',
                        name: 'claim',
                        authorization: [{
                            actor: account,
                            permission: 'owner',
                        }],
                        data: data.bigEndian
                    },
                ],
            };

            try {
                logToFile('Performing transaction:', transaction);
                const result = await myDfsWallet.transact(transaction, opts);
                // logToFile('Transaction result:', result);
                logToFile(`Claim successful. Claimed ${JSON.stringify(result)}.`);

                //提取 result li  "quantity": "0.******** DFS",

                claimCount++;
                lastClaimTime = now.toLocaleString();
                const state = { lastClaimTime: now.toLocaleString(), count: claimCount };
                fs.writeFileSync(STATE_FILE, JSON.stringify(state));
                logToFile(`Claim state updated: ${JSON.stringify(state)}`);
            } catch (error) {
                console.error('Transaction error:', error);
            }
        } else {
            logToFile('Balance is insufficient or does not meet threshold.');
        }

    };
    setInterval(checkBalanceAndClaim, 30 * 1000);
})();