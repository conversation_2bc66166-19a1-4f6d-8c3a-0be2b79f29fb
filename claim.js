const fs = require('fs'); // 引入文件系统模块
const dfsWallet = require('./dfs'); // 确保路径正确
const config = require('./config.json');
const { logToFile } = require('./logger');
const { log } = require('console');
const STATE_FILE = 'claim_state.json'; // 存储收矿时间和次数的文件名

// 初始化状态变量
let claimCount = 0;
let lastClaimTime = null;

// 加载之前的状态
try {
    if (fs.existsSync(STATE_FILE)) {
        const savedState = JSON.parse(fs.readFileSync(STATE_FILE, 'utf8'));
        claimCount = savedState.count || 0;
        lastClaimTime = savedState.lastClaimTime || null;
        logToFile(`Loaded previous state: count=${claimCount}, lastClaimTime=${lastClaimTime}`);
    }
} catch (error) {
    logToFile(`Error loading state: ${error.message}`);
}

// 初始化
(async () => {
    const myDfsWallet = new dfsWallet();
    await myDfsWallet.init('MyApp', config.account3.private_key);
    const account = config.account3.address;

    const opts = {
        useFreeCpu: true,
        blocksBehind: 3,
        expireSeconds: 3600
    };


    const checkBalanceAndClaim = async () => {
        const now = new Date(); // 添加当前时间
        logToFile('Starting balance check and claim...');
        const poolBalance = await myDfsWallet.getbalance('eosio.token', 'dfsbpsvoters', 'DFS');
        // console.log(analysis*1.05);
        if (parseFloat(poolBalance) >3) { 
            const data = myDfsWallet.nameToUint64(account);
            const transaction = {
                actions: [
                    {
                        account: 'dfsbpsvoters',
                        name: 'claim',
                        authorization: [{
                            actor: account,
                            permission: 'owner',
                        }],
                        data: data.bigEndian
                    },
                ],
            };

            try {
                logToFile('Performing transaction:', transaction);
                const result = await myDfsWallet.transact(transaction, opts);
                // logToFile('Transaction result:', result);
                logToFile(`Claim successful. Claimed ${JSON.stringify(result)}.`);

                // 提取 result 里面的 quantity 字段
                let claimedQuantity = null;
                let miningReward = null;
                try {
                    // 从交易结果中提取quantity和mining_reward信息
                    if (result && result.processed && result.processed.action_traces) {
                        for (const trace of result.processed.action_traces) {
                            // 查找 claimlog 操作中的 mining_reward
                            if (trace.act && trace.act.name === 'claimlog' && trace.act.data && trace.act.data.mining_reward) {
                                miningReward = trace.act.data.mining_reward;
                                logToFile(`Found mining_reward in claimlog: ${miningReward}`);
                            }

                            // 查找 inline_traces 中的 transfer 操作
                            if (trace.inline_traces) {
                                for (const inlineTrace of trace.inline_traces) {
                                    // 查找 transfer 操作中的 quantity
                                    if (inlineTrace.act &&
                                        inlineTrace.act.name === 'transfer' &&
                                        inlineTrace.act.data &&
                                        inlineTrace.act.data.quantity &&
                                        inlineTrace.act.data.memo === 'DFS Node mining reward') {
                                        claimedQuantity = inlineTrace.act.data.quantity;
                                        logToFile(`Found transfer quantity: ${claimedQuantity}`);
                                        break;
                                    }

                                    // 递归查找更深层的 inline_traces
                                    if (inlineTrace.inline_traces) {
                                        for (const deepTrace of inlineTrace.inline_traces) {
                                            if (deepTrace.act &&
                                                deepTrace.act.name === 'transfer' &&
                                                deepTrace.act.data &&
                                                deepTrace.act.data.quantity &&
                                                deepTrace.act.data.memo === 'DFS Node mining reward') {
                                                claimedQuantity = deepTrace.act.data.quantity;
                                                logToFile(`Found deep transfer quantity: ${claimedQuantity}`);
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                            if (claimedQuantity) break;
                        }
                    }

                    // 优先使用从transfer中提取的quantity，其次使用mining_reward
                    const finalQuantity = claimedQuantity || miningReward;

                    if (finalQuantity) {
                        logToFile(`Successfully extracted claimed quantity: ${finalQuantity}`);
                        console.log(`✅ 成功提取到收矿数量: ${finalQuantity}`);

                        // 解析数量和币种
                        const quantityMatch = finalQuantity.match(/^([\d.]+)\s+(\w+)$/);
                        if (quantityMatch) {
                            const amount = parseFloat(quantityMatch[1]);
                            const symbol = quantityMatch[2];
                            logToFile(`Parsed amount: ${amount} ${symbol}`);
                            console.log(`💰 收矿详情: ${amount} ${symbol}`);
                        }
                    } else {
                        logToFile('Could not extract quantity from transaction result');
                        console.log('⚠️ 无法从交易结果中提取quantity字段');
                    }
                } catch (extractError) {
                    logToFile(`Error extracting quantity: ${extractError.message}`);
                    console.error('提取quantity时出错:', extractError.message);
                }

                claimCount++;
                lastClaimTime = now.toLocaleString();
                const finalQuantity = claimedQuantity || miningReward;
                const state = {
                    lastClaimTime: now.toLocaleString(),
                    count: claimCount,
                    lastClaimedQuantity: finalQuantity, // 保存最后一次收矿的数量
                    transactionId: result.transaction_id // 保存交易ID
                };
                fs.writeFileSync(STATE_FILE, JSON.stringify(state, null, 2));
                logToFile(`Claim state updated: ${JSON.stringify(state)}`);
            } catch (error) {
                console.error('Transaction error:', error);
            }
        } else {
            logToFile('Balance is insufficient or does not meet threshold.');
        }

    };
    setInterval(checkBalanceAndClaim, 30 * 1000);
})();