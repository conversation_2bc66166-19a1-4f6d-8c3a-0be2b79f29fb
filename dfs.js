"use strict";

const { Api, JsonRpc } = require("eosjs");
const { JsSignatureProvider } = require("eosjs/dist/eosjs-jssig");
const config = require('./config.json');
const { Serialize } = require('eosjs');
// const fetch = require('node-fetch');
const fetch = (...args) =>
    import('node-fetch').then(({ default: fetch }) => fetch(...args));
const fs = require('fs'); // 引入 fs 模块
const CHAINID = '000d9cae502dd1cc895745e204f83cc892bc4c450f92a03ecd4fe057709853cc';
const {selectOptimalRpc}=require('./rpcSelector');
const FREECPU = {
    privateKey: '5JdBkvZva99uwBanXjGGhF4T7SrLpgTBipU76CD9QN4dFRPuD4N',
    account: 'dfs.service',
    authority: 'cpu',
    contract: 'dfsfreecpu11',
    actionName: 'freecpu',
};

function getFreeCpuApi(rpc, freeCpuPrivateKey) {
    // const httpEndpoint = rpcUrl;
    const private_keys = [freeCpuPrivateKey];

    const signatureProvider = new JsSignatureProvider(private_keys);
    // const rpc = new JsonRpc(httpEndpoint, { fetch });
    
    const eos_client = new Api({
        rpc,
        signatureProvider,
        textDecoder: new TextDecoder(),
        textEncoder: new TextEncoder(),
    });
    return eos_client;
}
function getApi(rpc, PrivateKey) {
    // const httpEndpoint = rpcUrl;
    const private_keys = [PrivateKey];

    const signatureProvider = new JsSignatureProvider(private_keys);
    // const rpc = new JsonRpc(httpEndpoint, { fetch });
    const eos_client = new Api({
        rpc,
        signatureProvider,
        textDecoder: new TextDecoder(),
        textEncoder: new TextEncoder(),
    });
    return eos_client;
}

class DfsWallet {
    constructor() {
        this.times = 30;
        this.appName = '';
        this.logoUrl = '';
        this.DFSWallet = null; // 这里设计为可连接的钱包对象
        this.api = null;
        this.rpc = null;
        this.chainId = CHAINID;
        this.freeCpuApi = null;
        // this.connect().then((res) => {
        //     console.log('connect - res', res);
        // });
    }

    async connect() {
        return new Promise((resolve) => {
            // 这里实现 DFSWallet 的连接逻辑（需要替换为实际的连接逻辑）
            if (this.DFSWallet != null) {
                resolve(true);
            } else {
                let times = 0;
                let timer = setInterval(() => {
                    // 检查 DFSWallet 是否已连接
                    if (this.DFSWallet != null || ++times === this.times) {
                        clearInterval(timer);
                        resolve(this.DFSWallet != null);
                    }
                }, 50);
            }
        });
    }

    async regIsConnect() {
        const isConnected = await this.connect();
        if (!isConnected) {
            throw new Error('dfsWallet not connected');
        }
    }

    async init(appName, private_key = config.private_key) {
        this.appName = appName;
        // this.rpcUrl = rpcUrl;
        const network = { chainId: this.chainId };
        // this.rpc = new JsonRpc(this.rpcUrl, { fetch });
        this.rpc=await selectOptimalRpc();

        // await this.regIsConnect();
        console.log('init', appName, this.rpc.endpoint, network);

        // console.log(config.private_key);

        this.api = getApi(this.rpc, private_key);
        this.freeCpuApi = getFreeCpuApi(this.rpc, FREECPU.privateKey);
        console.log("init done");

    }

    async login() {
        await this.regIsConnect();
        // 需要实现 DFSWallet 登录逻辑
        const id = await this.DFSWallet.login({
            chainId: this.chainId,
            newLogin: true,
        });
        return {
            channel: 'dfswallet',
            authority: id.accounts[0].authority,
            name: id.accounts[0].name,
            publicKey: id.accounts[0].publicKey,
        };
    }

    async logout() {
        // 需要实现 DFSWallet 登出逻辑
        // if (this.DFSWallet) {
        //     await this.DFSWallet.logout();
        // }
    }

    async transact(transaction, opts = {}) {
        // console.log('transact', transaction, opts);
        let resp;
        try {
            if (opts.useFreeCpu) {
                // delete opts.useFreeCpu;
                console.log("useFreeCpu");

                return await this.transactByFreeCpu(transaction, opts);
            }
            // console.log('api', this.api);
            console.log(" no useFreeCpu");
            resp = await this.api.transact(transaction, {
                blocksBehind: 3,
                expireSeconds: 3600,
                ...opts,
            });
            return resp;
        } catch (error) {
            const eMsg = this.dealError(error);
            throw eMsg;
        }
    }

    async transactByFreeCpu(transaction, opts = {}) {
        const accAuth = transaction.actions[0].authorization[0];
        transaction.actions.unshift({
            account: FREECPU.contract,
            name: FREECPU.actionName,
            authorization: [
                {
                    actor: FREECPU.account,
                    permission: FREECPU.authority,
                },
                accAuth,
            ],
            data: {
                user: accAuth.actor,
            },
        });

        try {
            // 当前账户签名
            let _PushTransactionArgs = await this.api.transact(transaction, {
                blocksBehind: 3,
                expireSeconds: 3600,
                sign: false,
                broadcast: false,
            });
            const availableKeys = await this.api.signatureProvider.getAvailableKeys();
            // console.log('availableKeys', availableKeys);

            const serializedTx = _PushTransactionArgs.serializedTransaction;

            const signArgs = {
                chainId: this.chainId,
                requiredKeys: availableKeys,
                serializedTransaction: serializedTx,
                abis: [],
            };
            let pushTransactionArgs = await this.api.signatureProvider.sign(signArgs);

            // 免CPU签名
            const freeCpuRequiredKeys = await this.freeCpuApi.signatureProvider.getAvailableKeys();
            const signArgsFreeCpu = {
                chainId: this.chainId,
                requiredKeys: freeCpuRequiredKeys,
                serializedTransaction: serializedTx,
                abis: [],
            };
            let pushTransactionArgsFreeCpu = await this.freeCpuApi.signatureProvider.sign(signArgsFreeCpu);
            pushTransactionArgs.signatures.unshift(pushTransactionArgsFreeCpu.signatures[0]);


            // 将操作广播出去
            if (!config.debug) {
                let push_result = await this.api.pushSignedTransaction(pushTransactionArgs);

                console.log('push_result', push_result);

                return push_result;
            } else {
                console.log('debug mode, skip pushSignedTransaction');
                return {
                    transaction_id: 'debug_mode_skip_pushSignedTransaction',
                };
            }

        } catch (error) {
            const eMsg = this.dealError(error);
            // console.log();

            throw eMsg;
        }
    }

    async sign(data = '') {
        if (!this.DFSWallet || !this.api) {
            throw new Error('Wallet not init');
        }
        const availableKeys = await this.api.signatureProvider.getAvailableKeys();
        return await this.DFSWallet.getArbitrarySignature(availableKeys[0], data);
    }

    dealError(e) {
        let back = {
            code: 999,
            message: e,
        };

        if (e.message === 'you have no permission for this operation') {
            back = {
                code: 999,
                message: e.message,
            };
            return back;
        }
        if (e.code === 0) {
            back = {
                code: 0,
                message: 'Cancel',
            };
            return back;
        }
        if (e.json && e.json.code === 500) {
            const dErr = e.json.error;
            const dealFun = [
                [
                    (code) => {
                        const codes = [3080004];
                        return codes.includes(Number(code));
                    },
                    (tErr) => {
                        const detail = tErr.details;
                        if (detail[0].message.indexOf('reached node configured max-transaction-time') !== -1) {
                            return {
                                code: '3080004_2',
                                message: 'reached node configured max-transaction-time',
                            };
                        }
                        return {
                            code: 402,
                            message: 'CPU Insufficient',
                        };
                    },
                ],
                [
                    (code) => {
                        const codes = [3080002, 3080001];
                        return codes.includes(Number(code));
                    },
                    (tErr) => {
                        return {
                            code: 402,
                            message: `${tErr.code == 3080001 ? 'RAM' : 'CPU'} Insufficient`,
                        };
                    },
                ],
                [
                    (code) => {
                        const codes = [3080006];
                        return codes.includes(Number(code));
                    },
                    () => {
                        return {
                            code: 3080006,
                            message: 'timeout',
                        };
                    },
                ],
                [
                    (code) => {
                        const codes = [3050003, 3010010];
                        return codes.includes(Number(code));
                    },
                    (tErr) => {
                        const detail = tErr.details;
                        if (detail[0].message.indexOf('INSUFFICIENT_OUTPUT_AMOUNT') !== -1) {
                            return {
                                code: 3050003,
                                message: 'INSUFFICIENT OUTPUT AMOUNT',
                            };
                        }
                        return {
                            code: tErr.code,
                            message: detail[0].message,
                        };
                    },
                ],
            ];
            const findErr = dealFun.find((v) => v[0](dErr.code));
            if (findErr) {
                back = findErr[1](dErr);
            } else {
                back = {
                    code: dErr.code,
                    message: dErr.details[0].message,
                };
            }
            return {
                code: back.code,
                message: back.message,
            };
        }
        return back;
    }


    async getTableRowsS(code, table, lower_bound = 1, upper_bound, index_position = 'secondary', key_type = 'i64') {


        console.log('getTableRowsS', code, table, lower_bound, upper_bound, index_position, key_type);




        let allRows = []; // 存储所有行数据

        try {
            //  while (true) {
            const response = await this.rpc.get_table_rows({
                json: true,
                code: code,  // 合约的代码
                scope: code,  // 作用域
                table: table,      // 表名称
                index_position: index_position, // 索引位置
                key_type: key_type, // 索引类型
                lower_bound: lower_bound, // 更新起始索引
                upper_bound: upper_bound, // 更新结束索引
                limit: -1             // 每次请求的行数
            });

            allRows = allRows.concat(response.rows); // 将获取的行数据合并到 allRows 中

            console.log(`Fetched ${response.rows.length} rows. Total: ${allRows.length}`);

            //    // 检查是否还有更多结果
            //     if (!response.more) break;
            // 更新 lowerBound 为当前结果的最后一个行的键值
            lower_bound = response.rows[response.rows.length - 1].id; // 替换为实际的主键
            console.log(`Fetching more rows with lower_bound: ${lower_bound}`);
            //  }

            // if (filterPid) {
            //     // 过滤出 pid 等于 66 的行
            //     const filteredRows = allRows.filter(row => row.pid === Number(filterPid)); // 替换 ‘pid’ 为实际字段名
            //     // 将获取的所有行写入到 JSON 文件
            //     // 输出过滤后的行数
            //     console.log(`Total filtered rows with pid === ${filterPid}: ${filteredRows.length}`);
            fs.writeFileSync(table + 'table_rows.json', JSON.stringify(allRows, null, 2), 'utf8');
            //     console.log('All data has been written to table_rows.json');
            //     return filteredRows;
            // } else {
            return allRows;
            // }


        } catch (error) {
            console.error('Error fetching table rows:', error);
        }

    }

    async GetTableRowswhitmid(mid) {

        console.log("GetTableRowswhitmid mid:", mid);

        const response = await this.rpc.get_table_rows({
            json: true,
            code: 'swapswapswap',  // 合约的代码
            scope: 'swapswapswap',  // 作用域
            table: 'markets',      // 表名称
            index_position: 'primary', // 索引位置
            key_type: 'i64', // 索引类型
            lower_bound: mid, // 更新起始索引
            upper_bound: mid, // 更新起始索引
            limit: 500              // 每次请求的行数
        });

        // console.log(response.rows);

        return response.rows;

    }

    async GetTableRows(load) {

        if (load) {
            // 读取本地数据
            console.log('读取本地数据');

            return this.loadData('table_rows_s.json')
        } else {
            const response = await this.rpc.get_table_rows({
                json: true,
                code: 'swapswapswap',  // 合约的代码
                scope: 'swapswapswap',  // 作用域
                table: 'markets',      // 表名称
                lower_bound: '', // 更新起始索引
                limit: 500              // 每次请求的行数
            });

            return response.rows;
        }

        //  console.log(response.rows);

    }

    getTableRows() {

        console.log('getTableRows');

        // 定义要读取的文件名
        const fileName = 'registrytable_rows.json';

        try {
            // 读取文件内容
            const data = fs.readFileSync(fileName, 'utf8');

            // 解析 JSON 数据
            const jsonData = JSON.parse(data);

            // console.log('Table rows data:', jsonData);

            // 如果需要，可以在这里对 jsonData 进行其他操作，比如过滤、遍历等
            console.log(`Total rows: ${jsonData.length}`); // 输出行数
            return jsonData;
        } catch (error) {
            console.error('Error reading the file:', error);
        }

    }
    // 读取 JSON 文件的函数
    loadData(name = 'projects_table_rows.json') {
        const data = fs.readFileSync(name, 'utf8');
        return JSON.parse(data);
    }
    // 主逻辑函数
    async getInitNftPriceById(id) {
        // 加载数据
        // const data = this.loadData();
        // // console.log(data);

        // // 假设 data 是个数组，遍历并查找指定 id 的项
        // const item = data.find(item => item.id === id); // 假设每个项目都有 id 属性

        // if (item) {
        //     console.log(`NFT Price for ID ${id}:`, item.init_nft_price); // 输出对应的初始 NFT 价格
        // } else {
        //     console.log(`Item with ID ${id} not found.`);
        // }
        // return item.init_nft_price;



        let allRows = []; // 存储所有行数据

        try {
            const response = await this.rpc.get_table_rows({
                json: true,
                code: 'dfs3protocol',  // 合约的代码
                scope: 'dfs3protocol',  // 作用域
                table: 'projects',      // 表名称
                index_position: 'primary', // 索引位置
                key_type: 'i64', // 索引类型
                lower_bound: id, // 更新起始索引
                upper_bound: id, // 更新起始索引
                limit: 500              // 每次请求的行数
            });
            allRows = allRows.concat(response.rows); // 将获取的行数据合并到 allRows 中

            console.log(`Fetched ${response.rows.length} rows. Total: ${allRows.length}`);

            return response.rows[0].init_nft_price;
        } catch (error) {

            console.error('Error fetching table rows:', error);
        }



    }

    // 封装 get_table_rows 函数
    getTableRows1 = async (code, scope, table, options = {}) => {
        try {
            const response = await this.rpc.get_table_rows({
                json: true,
                code,
                scope,
                table,
                ...options
            });
            return response;
        } catch (error) {
            console.error(`Error fetching ${table} from ${scope}:`, error);
            return null;
        }
    };

    // 查询账号余额
    async queryBalance(account) {
        const resp = await rpc.get_table_rows({
            json: true,              // Get the response as json
            code: 'eosio.token',     // Contract that we target
            scope: account,         // Account that owns the data
            table: 'accounts',       // Table name
            limit: 10,               // Maximum number of rows that we want to get
            reverse: false,          // Optional: Get reversed data
            show_payer: false,       // Optional: Show ram payer
        });
        return resp.rows;
    }


     assetidtohex(num, isLittleEndian = true) {
        // 创建一个 8 字节的缓冲区
        const buffer = Buffer.alloc(8);
    
        // 根据字节序选择适当的写入方法
        if (num <= 255) {
            if (isLittleEndian) {
                buffer.writeUInt8(num, 0);
            } else {
                buffer.writeUInt8(num, 0);
            }
        } else if (num <= 65535) {
            if (isLittleEndian) {
                buffer.writeUInt16LE(num, 0); // 小端
            } else {
                buffer.writeUInt16BE(num, 0); // 大端
            }
        } else {
            if (isLittleEndian) {
                buffer.writeUInt32LE(num, 0); // 小端
            } else {
                buffer.writeUInt32BE(num, 0); // 大端
            }
        }
    
        // 将缓冲区转换为十六进制字符串
        const hexString = buffer.toString('hex');
    
        console.log(hexString); // 输出相应的十六进制字符串
        return hexString;
    }

    nameToUint64(name) {
        // 创建序列化缓冲区
        const buffer = new Serialize.SerialBuffer({
            textEncoder: new TextEncoder(),
            textDecoder: new TextDecoder(),
        });

        // 使用 pushName 将 EOS 名称编码到缓冲区
        buffer.pushName(name);

        // 提取缓冲区前 8 字节 (uint64)
        const uint8Array = buffer.array.slice(0, 8);

        // 将 8 字节解析为 BigInt 格式的 uint64（小端序）
        let uint64LittleEndian = 0n;
        for (let i = 0; i < 8; i++) {
            uint64LittleEndian |= BigInt(uint8Array[i]) << BigInt(8 * i); // 小端序解析
        }

        // 转换为大端序：反转字节顺序
        const uint8ArrayBigEndian = uint8Array.slice().reverse(); // 创建反转数组
        let uint64BigEndian = 0n;
        for (let i = 0; i < 8; i++) {
            uint64BigEndian |= BigInt(uint8ArrayBigEndian[i]) << BigInt(8 * i); // 按反转顺序解析
        }
        return {
            littleEndian: uint64LittleEndian.toString(16).padStart(16, '0'),
            bigEndian: uint64BigEndian.toString(16).padStart(16, '0'),
        };
    }
    getbalance = async (code, account, symbol = 'DFS') => {
        try {
            const response = await this.rpc.get_currency_balance(code, account, symbol);
            console.log(response);
            
            return response[0];
        } catch (error) {
            console.error(`Error fetching balance for ${account}:`, error);
            return null;
        }
    };


    get_transacton = async (id) => {
        try {
            const response = await this.rpc.history_get_transaction(id);
            console.log("history_get_transaction");
            console.log(response);
            return response;
        } catch (error) {
            console.error(`Error fetching transaction for ${id}:`, error);
            return null;
        }
    };
    // getbalance = async (account) => {
    //     try {
    //         const response = await this.rpc.get_currency_balance('dfsppptokens', account, 'DOGS');
    //         return response[0];
    //     } catch (error) {
    //         console.error(`Error fetching balance for ${account}:`, error);
    //         return null;
    //     }
    // };
}


module.exports = DfsWallet;
