const { Api, JsonRpc } = require('eosjs');
const fetch = (...args) =>
    import('node-fetch').then(({ default: fetch }) => fetch(...args));
const config = require('./config.json');
const pako = require('pako');
// const rpc = new JsonRpc(config.dfs_server_url, { fetch });
const { selectOptimalRpc, selectOptimalRpc1 } = require('./rpcSelector');
const fs = require('fs');
const path = require('path');
const { logToFile } = require('./logger.js');
const { sendDingtalkMessage } = require('./dd.js');
let rpc;
// 定义表状态
const tableStates = {
    posts: {
        lastIds: new Set(), // 用于存储已查看的 row.id
        firstRun: true, // 标记是否为首次运行
        issending: true, // 是否可以发送消息
    },
    logs: {
        lastIds: new Set(), // 用于存储已查看的 row.id
        firstRun: true, // 标记是否为首次运行
        issending: true, // 是否可以发送消息
    },
    projects: {
        lastIds: new Set(), // 用于存储已查看的 row.id
        firstRun: true, // 标记是否为首次运行
        issending: true, // 是否可以发送消息
    },
    donatelogs: {
        lastIds: new Set(), // 用于存储已查看的 row.id
        firstRun: true, // 标记是否为首次运行
        issending: true, // 是否可以发送消息
    },
};


// 常量定义
const CONTRACT_CODE = 'dfs3protocol';
const TABLE_PROJECTS = 'projects';
const DEFAULT_LIMIT = 1500;
const pppliquidity = 'pppliquidity';
const now = new Date();
const options = {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false // 24小时制
};


async function checkRpcConnection(rpc) {
    try {
        // 尝试获取节点信息
        await rpc.get_info();
        console.log(`RPC is connected to: ${rpc.endpoint}`);
        return true; // 如果成功，返回 true
    } catch (error) {
        console.error('RPC 连接失败:', error.message);
        return false; // 连接失败，返回 false
    }
}

// 封装 get_table_rows 函数
const getTableRows = async (code, scope, table, options = {}) => {
    try {
        if (!rpc || !(await checkRpcConnection(rpc))) {
            console.log('正在重新选择最优 RPC 节点...');
            rpc = await selectOptimalRpc();
        }
        const response = await rpc.get_table_rows({
            json: true,
            code,
            scope,
            table,
            ...options
        });
        return response;
    } catch (error) {
        console.error(`Error fetching ${table} from ${scope}:`, error);
        return null;
    }
};

// 通用获取表数据的函数
async function GetTableRows(table, params) {
    try {
        if (!rpc || !(await checkRpcConnection(rpc))) {
            console.log('正在重新选择最优 RPC 节点...');
            rpc = await selectOptimalRpc();
        }
        const response = await rpc.get_table_rows({
            json: true,
            code: params.code || CONTRACT_CODE,
            scope: params.scope || CONTRACT_CODE,
            index_position: params.indexPosition || 1,
            table: table,
            key_type: 'i64',
            limit: params.limit || DEFAULT_LIMIT,
            lower_bound: params.lowerBound,
            upper_bound: params.upperBound,
        });
        return response.rows;
    } catch (error) {
        console.error('Error fetching table rows:', error);
        throw new Error('获取表数据失败');
    }
}
/**
 * 生成一个随机字符或表情
 * @returns {string} - 随机字符或表情
 */
function generateRandomChar() {
    const chars = '😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌😜😝😛🤑😎😘😗😙😚🤗🤩🤔😏😒😞😔😟😕🙁☹️😣😖😫😩😠😡🤬';
    // 将字符串转换为数组
    const charsArray = Array.from(chars);
    const randomIndex = Math.floor(Math.random() * charsArray.length);

    return charsArray[randomIndex]; // 随机返回字符
}

// 处理消息的函数
function createMessage(type, content) {
    const randomChar = generateRandomChar();
    content = `${content}${randomChar}`; // 添加随机字符
    // 创建消息对象
    let message = {
        type: type,
        content: content,
        timestamp: new Date().toISOString() // 添加时间戳
    };

    // 返回 JSON 字符串格式的消息
    // return JSON.stringify(message);
    return content;
}



async function calculateIssending(row, inValue, limit = 1) {
    // 获取价格数据
    const res = await getTableRows('linklinklink', 'linklinklink', 'prices', {
        json: true,
        limit: limit,
        index_position: 1,
        reverse: true,
        show_payer: false,
        key_type: 'i64',
        lower_bound: row.mid,
        upper_bound: row.mid,
    });

    let price0_last = 0;
    let price1_last = 0;

    if (res && res.rows && res.rows.length > 0) {
        price0_last = res.rows[0].price0_last;
        price1_last = res.rows[0].price1_last;
    }

    logToFile(`mid = ${row.mid} price0_last = ${price0_last} price1_last = ${price1_last}`);

    // 计算出价值
    let outValueWithPrice = inValue * price1_last;
    logToFile(`计算价格: price1 = ${price1_last} 价值: ${outValueWithPrice}`);
    // 返回布尔值，根据计算结果判断是否发送
    return outValueWithPrice > 50;
}


// 创建一个延时函数
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
// 检查更新的主函数
const checkForUpdates = async (code, scope, table, limit) => {
    const tableState = tableStates[table]; // 获取当前表的状态
    if (!tableState) {
        console.error(`未找到表状态: ${table}`);
        return;
    }

    logToFile(`检查 ${table} 更新...`);

    try {
        const postsData = await getTableRows(code, scope, table, {
            json: true,
            limit: limit,
            index_position: 1,
            reverse: true,
            show_payer: false,
            key_type: 'i64',
        });
        let intsy = null;
        let outsy = null;
        if (postsData && postsData.rows) {
            logToFile(`Found ${postsData.rows.length} new ${table}`);
            postsData.rows.forEach(async row => {
                // 检查 row.id 是否已经存在
                if (!tableState.lastIds.has(row.id)) {
                    // logToFile(row);
                    // 添加到已查看的 ID 集合
                    tableState.lastIds.add(row.id);
                    let message;
                    let msgtpye;
                    if (table === 'logs') {
                        // 假设 row.create_time 是 ISO 8601 格式的字符串，加上8小时
                        let createTimeWithOffset = new Date(row.create_time);
                        createTimeWithOffset.setHours(createTimeWithOffset.getHours() - 4);
                        let formattedTime = createTimeWithOffset.toLocaleString();
                        const beijingTime = new Intl.DateTimeFormat('zh-CN', options).format(createTimeWithOffset);
                        // console.log(beijingTime);

                        msgtpye = 'swap';
                        // 提取数值和符号
                        const inValue = parseFloat(row.in.match(/(\d+(\.\d+)?)/)[0]);
                        const inSymbol = row.in.match(/[A-Za-z]+/)[0];
                        const outValue = parseFloat(row.out.match(/(\d+(\.\d+)?)/)[0]);
                        const outSymbol = row.out.match(/[A-Za-z]+/)[0];
                        intsy = inSymbol;
                        outsy = outSymbol;
                        let op = 'swap';
                        if (row.user === pppliquidity) {
                            op = '回购';
                            msgtpye = 'swap';
                        }
                        // 计算价格 低于50的不发送

                        let outValueWithPrice = 0;

                        if (inSymbol === "DFS") {
                            outValueWithPrice = inValue;
                        } else if (outSymbol === "DFS") {
                            outValueWithPrice = outValue;
                        }

                        logToFile(tableState.lastIds.size);
                        message = `${op} ${inValue.toFixed(1)} ${inSymbol} -> ${outValue.toFixed(1)} ${outSymbol}
----------------
ID: ${row.id}
用户: ${row.user}
输入: ${inValue} ${inSymbol}
输出: ${outValue} ${outSymbol}
创建时间: ${formattedTime}
----------------
`;
                        if (outValueWithPrice > 50 && tableState.lastIds.size > 10) {
                            tableState.issending = true;
                        } else {
                            tableState.issending = false;
                        }


                    } else if (table === 'posts') {
                        // 解码和解压缩内容
                        logToFile(` ${row.author} ${row.id}`);
                        // let decodedBytes = Uint8Array.from(atob(row.content), c => c.charCodeAt(0));
                        let decodedBytes = new Uint8Array(Buffer.from(row.content, 'base64'));

                        let resultString = '';

                        // 尝试解压缩
                        try {
                            let decompressedData = pako.inflate(decodedBytes);
                            resultString = new TextDecoder("utf-8").decode(decompressedData);
                            logToFile(resultString); // 输出解压缩后的内容
                        } catch (e) {
                            console.error("解压失败:", e);
                        }
                        msgtpye = '推文';
                        message = `
${row.author} 发布新帖：
----------------
${resultString}
----------------
`;
                    } else if (table === 'projects') {

                        logToFile(row);
                        msgtpye = '新图'
                        message = `
新图发射：
----------------
创建者: ${row.creator}
项目名称: ${row.project_name}
描述: ${row.desc || '无'}
初始 NFT 数量: ${row.init_nft_number || '无'}
初始 NFT 价格: ${row.init_nft_price || '无'}
代币每 NFT: ${row.token_per_nft || '无'}
最大代币供应量: ${row.max_token_supply || '无'}
----------------
`;
                    } else if (table === 'donatelogs') {




                        logToFile(`捐赠 ${row}`)


                    }
                    // 如果是首次运行，不进行通知
                    if (tableState.firstRun) {
                        tableState.firstRun = false; // 修改标志为非首次运行
                    } else {   // 否则发送通知
                        // 发送消息
                        if (tableState.issending) {
                            const formattedMessage = createMessage(msgtpye, message); // 创建格式化的消息
                            let webhookUrl = config.newnftbot;
                            if (msgtpye === 'swap') {
                                webhookUrl = config.swapbot;
                            }
                            else if (msgtpye === '推文') {
                                webhookUrl = config.postbot;
                            }
                            else if (msgtpye === '新图') {
                                webhookUrl = config.newnftbot;
                            }
                            else if (msgtpye === '捐赠') {
                                webhookUrl = config.donatebot;
                            }
                            else {
                                console.error('未知消息类型')
                            }
                            sendDingtalkMessage(webhookUrl, formattedMessage); // 发送消息
                            if (intsy === "DOGS" || outsy === "DOGS") {   // 发送消息
                                sendDingtalkMessage(config.dogsbot, formattedMessage); // 发送消息
                            }
                        }
                    }
                }
            });
        }
    } catch (error) {
        console.error('Error fetching table rows:', error);
    }
};

const remindersFilePath = path.join(__dirname, 'remindersState.json');
let remindersState = new Map(); // 全局状态对象，保存每个项目的提醒状态

// 读取文件内容并加载到 remindersState
function loadRemindersState() {
    if (fs.existsSync(remindersFilePath)) {
        const data = fs.readFileSync(remindersFilePath, 'utf8');
        if (data.trim() !== '') {
            const parsedData = JSON.parse(data);
            remindersState = new Map(parsedData);
        }
    } else {
        saveRemindersState(); // 如果文件不存在，创建一个空文件
    }
}

// 保存 remindersState 到文件
function saveRemindersState() {
    const data = JSON.stringify([...remindersState]);
    fs.writeFileSync(remindersFilePath, data, 'utf8');
    logToFile('Reminders state saved to file.');
}


// 存储每个项目的上一次读取的数据
const lastReadData = {};

// 监控捐赠
async function startProjectdonate() {
    try {
        // 获取项目数据
        let projects = await GetTableRows(TABLE_PROJECTS, {});
        logToFile('Projects:', projects.length);

        for (const project of projects) {
            const projectId = project.id;

            // 获取当前项目的最新数据
            const currentData = await getTableRows('dfspppreward', projectId, 'donatelogs', { limit: 1 });

            // 将当前数据的关键属性（如时间戳、值等）提取出来进行比较
            const currentProjectData = currentData.rows.length > 0 ? currentData.rows[0] : null;
            let message = currentProjectData ? `捐赠 ${currentProjectData.from} ${currentProjectData.quantity} 到 ${project.project_name} 项目！` : '';

            if (currentProjectData) {
                // 检查 lastReadData 是否已有数据
                if (!lastReadData[projectId]) {
                    // 第一次读取，不发送通知，直接保存数据
                    lastReadData[projectId] = currentProjectData;
                    logToFile(`首次读取项目 ${projectId}: ${message}`);
                } else {
                    // 比较是否与上一次的读取数据不同
                    if (JSON.stringify(lastReadData[projectId]) !== JSON.stringify(currentProjectData)) {
                        // 如果变动，记录变化
                        logToFile(`项目 ${projectId} 数据变动:`, currentProjectData);
                        // 发送通知
                        const formattedMessage = createMessage('捐赠', message); // 创建格式化的消息
                        sendDingtalkMessage(config.newnftbot, formattedMessage)
                    } else {
                        // 如果没有变动，则跳过 
                        logToFile(message);
                    }
                    // 更新 lastReadData 为当前读取的数据
                    lastReadData[projectId] = currentProjectData;
                }
            }
        }
    } catch (error) {
        console.error('Error in startProjectdonate function:', error);
    } finally { }
}



let isUpdating = false; // 跟踪当前是否有正在进行的更新


async function startProjectCountdowns() {
    logToFile('开始监控项目倒计时...', true);

    if (isUpdating) {
        logToFile('更新已在进行中，请稍后再试。');
        return; // 如果正在更新，则直接返回
    }

    isUpdating = true; // 设置为正在更新状态

    try {
        // 获取项目数据
        let projects = await GetTableRows(TABLE_PROJECTS, {});
        logToFile(`Projects: ${projects.length}`, true)
        // 获取指定的id
        const excludedIds = [66, 109, 119, 118, 98, 161];
        projects = projects.filter(project => excludedIds.includes(project.id));
        logToFile(`Projects: ${projects.length}`, true)
        projects.forEach(project => {
            const per_round = project.sec_per_round / 3600;
            const lastRoundTime = new Date(project.last_round);
            lastRoundTime.setHours(lastRoundTime.getHours() + per_round);
            let startTime = 1 * 60 * 1000; // 开始时间 提前 1 分钟
            const countdownEnd = new Date(lastRoundTime.getTime() + 12 * 60 * 60 * 1000); // 添加 12 小时

            // 初始化 remindersSent 状态（如果不存在）
            if (!remindersState.has(project.id)) {
                remindersState.set(project.id, {
                    oneHour: false,
                    thirtyMinutes: false,
                    fifteenMinutes: false,
                    threeMinutes: false,
                });
            }

            const updateRemainingTime = () => {
                const now = new Date();
                const remainingTime = countdownEnd - now - (12 * 60 * 60 * 1000);
                let remindersSent = remindersState.get(project.id); // 获取当前项目的提醒状态

                // console.log('remainingTime'+remainingTime);

                if (remainingTime > 0) {
                    const hours = Math.floor((remainingTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);

                    // 发送提醒的函数
                    function checkAndSendReminder(remainingTime, threshold, message, reminderKey) {
                        if (remainingTime <= threshold && !remindersSent[reminderKey]) {
                            logToFile(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + config.newnftbot);
                            logToFile(reminderKey);
                            logToFile(remindersSent[reminderKey]);


                            logToFile(message);
                            const formattedMessage = createMessage('抢图', message); // 创建格式化的消息
                            sendDingtalkMessage(config.newnftbot, formattedMessage)
                            remindersSent[reminderKey] = true; // 设置为已发送

                            // 更新 remindersState
                            remindersState.set(project.id, remindersSent);
                            saveRemindersState(); // 仅发送通知时保存
                        } else {
                            //     logToFile(reminderKey);   
                            // logToFile(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                            //    logToFile(remindersSent[reminderKey]);   
                            // logToFile(remainingTime);
                            // logToFile(threshold);
                            //    logToFile(message);   
                        }
                    }



                    checkAndSendReminder(remainingTime, 3 * 60 * 1000, `#${project.id}  ${project.project_name} 将在 3 分钟后开始抢购！`, 'threeMinutes');
                    checkAndSendReminder(remainingTime, 10 * 60 * 1000, `#${project.id}  ${project.project_name} 将在 10 分钟后开始抢购！`, 'fifteenMinutes');
                    // checkAndSendReminder(remainingTime, 30 * 60 * 1000, `#${project.id}  ${project.project_name} 将在 30 分钟后开始抢购！`, 'thirtyMinutes');
                    // checkAndSendReminder(remainingTime, 1 * 60 * 60 * 1000, `#${project.id}  ${project.project_name} 将在 ${minutes} 分钟后开始抢购！`, 'oneHour');
                    // console.log(`Project: #${project.id} has ${hours} hours, ${minutes} minutes, and ${seconds} seconds left.`);
                } else {
                    logToFile(`Project: ${project.id} has now entered the next round.`);
                    clearInterval(interval); // 清除倒计时定时器

                    // 重置 remindersSent 状态
                    remindersState.set(project.id, {
                        oneHour: false,
                        thirtyMinutes: false,
                        fifteenMinutes: false,
                        threeMinutes: false,
                    });
                    saveRemindersState(); // 倒计时结束时保存
                    logToFile(`Reminders for project ${project.id} have been reset.`);
                }
            };

            // 每秒更新一次倒计时
            const interval = setInterval(updateRemainingTime, 1000);
            // 立即调用一次进行初始输出
            updateRemainingTime();
        });
    } catch (error) {
        console.error('Error in startProjectCountdowns function:', error);
    } finally {
        isUpdating = false; // 设置为不在更新状态
    }
}

// 程序启动时加载 remindersState
loadRemindersState();
// 每 10 分钟更新一次
setInterval(startProjectCountdowns, 5 * 60 * 60 * 1000);

// 初始化时运行一次
startProjectCountdowns();


//捐赠
// setInterval(startProjectdonate, 5 * 1000);

// startProjectdonate();

// 初始化函数，用于开始轮询
const startMonitoring = (code, scope, table, interval = 5000, limit = 1,) => {
    // 每隔指定时间检查更新
    setInterval(() => {
        checkForUpdates(code, scope, table, limit);
    }, interval);
};

// 导出函数以供外部使用
module.exports = {
    startMonitoring,
    startProjectCountdowns,
};





