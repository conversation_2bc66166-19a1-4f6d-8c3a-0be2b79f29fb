const fetch = (...args) =>
    import('node-fetch').then(({ default: fetch }) => fetch(...args));

// 钉钉机器人消息发送限制
const messageQueues = {}; // 每个 webhookUrl 独立的消息队列
const rateLimits = {}; // 每个 webhookUrl 的计时器和已发送消息计数
const retryLimit = 3; // 每条消息的最大重试次数
let canSendMessages = true; // 消息发送开关
// 创建一个延时函数
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
/**
 * 发送钉钉消息
 * @param {string} webhookUrl - 钉钉 Webhook 地址
 * @param {string} content - 要发送的消息内容
 */
async function sendDingtalkMessage(webhookUrl, content) {

    // 检查是否允许发送消息
    if (!canSendMessages) {
        console.log('消息发送被禁用，无法发送:', content);
        return; // 如果发送被禁用，则返回不发送
    }

    await delay(200); // 延时 1 秒，避免频繁发送导致钉钉接口报错

    // 初始化队列和限制状态
    if (!messageQueues[webhookUrl]) {
        messageQueues[webhookUrl] = []; // 消息队列
    }
    if (!rateLimits[webhookUrl]) {
        rateLimits[webhookUrl] = {
            startTime: null, // 10分钟计时器的起始时间
            sentCount: 0 // 已发送消息计数
        };
    }

    // 将消息添加到队列（附带重试计数）
    messageQueues[webhookUrl].push({ content: '预警 ' + content, retries: 0 });

    // 开始处理队列
    await processQueue(webhookUrl);
}

/**
 * 处理指定 Webhook URL 的消息队列
 * @param {string} webhookUrl - 钉钉 Webhook 地址
 */
async function processQueue(webhookUrl) {
    const queue = messageQueues[webhookUrl];
    const limit = rateLimits[webhookUrl];

    // 如果队列为空，直接返回
    if (!queue || queue.length === 0) return;

    // 如果未开始计时，则初始化计时器
    if (!limit.startTime) {
        limit.startTime = Date.now();
    }

    // 检查 1 分钟是否已过期
    const elapsedTime = Date.now() - limit.startTime;
    if (elapsedTime >= 1 * 60 * 1000) {
        // 如果 10 分钟已过，重置计时器和计数
        limit.startTime = Date.now();
        limit.sentCount = 0;
    }

    // 如果已达到 1 分钟内的发送限制
    if (limit.sentCount >= 20) {
        const waitTime = 1 * 60 * 1000 - elapsedTime; // 剩余等待时间
        console.log(
            `已达到 1 分钟内的发送限制，等待 ${Math.ceil(waitTime / 1000)} 秒后继续发送 [${webhookUrl}]`
        );
        await new Promise((resolve) => setTimeout(resolve, waitTime));
        // 重新初始化计时器和计数
        limit.startTime = Date.now();
        limit.sentCount = 0;
    }

    // 发送队列中的消息（最多发送一条）
    const message = queue.shift(); // 从队列中取出一条消息
    try {
        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                msgtype: 'text',
                text: {
                    content: message.content
                }
            })
        });

        const data = await response.json();
        if (response.ok) {
            console.log(`消息发送成功 [${webhookUrl}]:`, data);
            limit.sentCount += 1; // 增加已发送计数
        } else {
            console.error(`消息发送失败 [${webhookUrl}]:`, data);
            throw new Error(data.errmsg || '发送失败');
        }
    } catch (error) {
        console.error(`请求发生错误 [${webhookUrl}]:`, error);
        // 如果发生错误，并且未超过重试次数，则重新加入队列
        if (message.retries < retryLimit) {
            message.retries += 1;
            queue.unshift(message); // 重新放回队列头部
            console.log(
                `消息重试，第 ${message.retries} 次 [${webhookUrl}]: ${message.content}`
            );
        } else {
            console.error(
                `消息重试失败，已丢弃 [${webhookUrl}]: ${message.content}`
            );
        }
    }

    // 如果队列中仍有消息，继续处理
    if (queue.length > 0) {
        await processQueue(webhookUrl); // 使用 await 确保同步处理
    }
}

// 导出函数以供其他文件使用
module.exports = { sendDingtalkMessage };
