// 导入相关模块
const fs = require('fs');
const path = require('path');
const dfsWallet = require('./dfs.js'); // 确保路径正确
const config = require('./config.json');
const { logToFile } = require('./logger.js');
const { sendDingtalkMessage } = require('./dd.js');
const { log } = require('console');
const fetch = (...args) => import('node-fetch').then(({ default: fetch }) => fetch(...args));

// 状态文件路径
const stateFilePath = './trade_state.json';
const configPath = path.join(__dirname, 'config.json');

// 全局状态
let state = {};

// 加载配置文件
const loadConfig = () => {
  try {
    const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    return configData;
  } catch (error) {
    console.error('Failed to load config:', error);
    return config; // 默认返回原来的配置
  }
};

// 监控配置文件变化
fs.watch(configPath, (eventType) => {
  if (eventType === 'change') {
    console.log('Configuration file changed. Reloading...');
    const newConfig = loadConfig();
    Object.assign(config, newConfig); // 动态更新配置
  }
});

// 加载状态
const loadState = () => {
  if (fs.existsSync(stateFilePath)) {
    try {
      const fileContent = fs.readFileSync(stateFilePath, 'utf8');
      state = JSON.parse(fileContent);
      console.log('State loaded:', state);
    } catch (error) {
      console.error('Failed to load state:', error);
    }
  } else {
    console.log('State file not found, starting fresh.');
  }
};

const getcostamount = async (wallet) => {
  const tableRows1 = await wallet.getTableRows1(
    'loglogloglog',
    'loglogloglog',
    'logs',
    { limit: 1, json: true, reverse: true, index_position: 1, key_type: 'i64' }
  );

  console.log(`tableRows1: ${tableRows1.rows[0]}`);
};



// 更新状态函数
const updateStrategyState = async (out, strategyState, currentPrice, amount, isBuy) => {
  if (isBuy) {
    const totalCost = strategyState.averageBuyPrice * strategyState.positionAmount + currentPrice * amount;
    strategyState.investmentAmount += out;
    strategyState.positionAmount += amount;
    strategyState.averageBuyPrice = totalCost / strategyState.positionAmount;
    strategyState.targetSellPrice = strategyState.averageBuyPrice * 1.05;
    strategyState.inTrade = true;

  } else {
    strategyState.investmentAmount = 0;
    strategyState.positionAmount = 0;
    strategyState.averageBuyPrice = 0;
    strategyState.targetBuyPrice = currentPrice * 0.95;
    strategyState.targetSellPrice = 0;
    strategyState.inTrade = false;
    strategyState.currentAdditions = 0;
    strategyState.waitingToBuy = true;
  }
};

// 保存状态
const saveState = () => {
  try {
    fs.writeFileSync(stateFilePath, JSON.stringify(state, null, 2), 'utf8');
    console.log('State saved:', state);
  } catch (error) {
    console.error('Failed to save state:', error);
  }
};

// 初始化每个币种和策略的状态
const initState = (strategies) => {
  for (const strategy of strategies) {
    const { coin, strategyId } = strategy;
    if (!state[coin]) {
      state[coin] = {};
    }
    if (!state[coin][strategyId]) {
      state[coin][strategyId] = {
        targetBuyPrice: 0,
        targetSellPrice: 0,
        inTrade: false,
        investmentAmount: 0,
        positionAmount: 0,
        averageBuyPrice: 0,
        maxAdditions: strategy.maxAdditions || 3,
        currentAdditions: 0,
        waitingToBuy: false,
      };
    }
  }
};

// 交易逻辑
const startSwapProcess = async (strategies) => {
  console.log('Starting swap script...');
  console.log(`Address: ${config.account.address}`);

  // 加载状态
  loadState();
  initState(strategies);

  // 创建和初始化 DfsWallet 实例
  const myDfsWallet = new dfsWallet();
  await myDfsWallet.init('MyApp', config.account.private_key);

  const opts = { useFreeCpu: true, blocksBehind: 3, expireSeconds: 3600 };
  // 封装计算最小兑换值的函数
  const calculateEstimatedOutput = (amount, currentPrice, slippage = 0.1) => {
    const exchangeRate = 1 / currentPrice;  // 计算兑换率
    let estimatedOutput = amount * exchangeRate;  // 计算初始输出
    estimatedOutput -= estimatedOutput * slippage; // 扣除滑点
    return estimatedOutput;
  };
  // 买入操作
  const buy = async (contract, sym0, strategy, currentPrice) => {
    const { coin, strategyId, mid, amount } = strategy;
    const sym = coin;
    // 计算最小兑换值（考虑滑点 10%）;
    let estimatedOutput = calculateEstimatedOutput(amount, currentPrice, 0.1)
    const memo = `swap:${mid}:${estimatedOutput.toFixed(8)}`;
    const formattedAmount = `${amount.toFixed(8)} ${sym0}`;
    const strategyState = state[coin][strategyId];

    console.log(`Starting Buy operation for ${coin}, strategy ${strategyId}...`);
    console.log(`Buying at price: ${currentPrice}`);
    console.log('Memo:', memo, 'Amount:', formattedAmount);

    const transaction = {
      actions: [
        {
          account: contract,
          name: 'transfer',
          authorization: [{ actor: config.account.address, permission: 'active' }],
          data: { from: config.account.address, to: 'swapswapswap', quantity: formattedAmount, memo },
        },
      ],
    };
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms));
    try {
      const result = await myDfsWallet.transact(transaction, opts);
      console.log('Buy successful:', result);
      await delay(4 * 1000); // 延迟 4s 再获取交易记录
      const tableRows1 = await myDfsWallet.getTableRows1(
        'loglogloglog',
        'loglogloglog',
        'logs',
        { limit: 10, json: true, reverse: true, index_position: 1, key_type: 'i64' }
      );

      const filteredRows = tableRows1.rows.filter(row => row.user === config.account.address);
      console.log(filteredRows);

      let out = filteredRows[0].out.split(' ')[0]
      logToFile(`out: ${out}`);
      updateStrategyState(parseFloat(out), strategyState, currentPrice, amount, true);
      sendDingtalkMessage(config.swapbot2, 'buy success', JSON.stringify(strategyState, null, 2));
      saveState();
    } catch (buyError) {
      console.error('Buy failed:', buyError);
    }
  };

  // 卖出操作
  const sell = async (contract, strategy, currentPrice,currentPrice1) => {
    const { coin, strategyId, mid } = strategy;
    const sym = coin;
    const strategyState = state[coin][strategyId];

    let sellAmount = strategyState.investmentAmount; // 卖出全部持仓

    let estimatedOutput = calculateEstimatedOutput(sellAmount, currentPrice, 0.1)
    const memo = `swap:${mid}:${estimatedOutput.toFixed(8)}`;
    const formattedAmount = `${sellAmount.toFixed(8)} ${sym}`;

    console.log(`Starting Sell operation for ${coin}, strategy ${strategyId}...`);
    console.log(`Selling at price: ${currentPrice}`);
    console.log('Memo:', memo, 'Amount:', formattedAmount);
    const transaction = {
      actions: [
        {
          account: contract,
          name: 'transfer',
          authorization: [{ actor: config.account.address, permission: 'active' }],
          data: { from: config.account.address, to: 'swapswapswap', quantity: formattedAmount, memo },
        },
      ],
    };
    try {
      const sellResult = await myDfsWallet.transact(transaction, opts);
      console.log('Sell successful:', sellResult);
      updateStrategyState(0, strategyState, currentPrice1, sellAmount, false);
      sendDingtalkMessage(config.swapbot2, 'sell success', JSON.stringify(strategyState, null, 2));
      saveState();
    } catch (sellError) {
      console.error('Sell failed:', sellError);
    }

  };

  // 核心逻辑函数
  const checkAndTrade = async (strategy) => {
    const { coin, strategyId, mid, amount } = strategy;
    const strategyState = state[coin][strategyId];
    let contract0 = null;
    let contract1 = null;
    let sym0 = null;
    let currentPrice1 = 0;
    let currentPrice0 = 0;

    try {
      logToFile(`Checking for ${coin}, strategy ${strategyId}...`);
      const tableRows1 = await myDfsWallet.getTableRows1(
        'swapswapswap',
        'swapswapswap',
        'markets',
        { upper_bound: mid, lower_bound: mid, limit: 1 }
      );


      if (tableRows1.rows.length === 0) {
        console.warn(`No market data found for ${coin}, strategy ${strategyId}. Retrying...`);
        return;
      } else {
        currentPrice1 = parseFloat(tableRows1.rows[0].reserve0) / parseFloat(tableRows1.rows[0].reserve1);
        currentPrice0 = parseFloat(tableRows1.rows[0].reserve1) / parseFloat(tableRows1.rows[0].reserve0);
        contract0 = tableRows1.rows[0].contract0;
        contract1 = tableRows1.rows[0].contract1;
        sym0 = tableRows1.rows[0].sym0.split(',')[1];
      }

      if (strategyState.inTrade) {
        logToFile(`coin ${coin},  currentprice: ${currentPrice1.toFixed(3)}, averagebuyprice: ${strategyState.averageBuyPrice.toFixed(3)}, targetsellprice: ${strategyState.targetSellPrice.toFixed(3)}`);
      } else {
        logToFile(`coin ${coin},  currentprice: ${currentPrice1.toFixed(3)}, targetbuyprice: ${strategyState.targetBuyPrice.toFixed(3)}`);
      }

      if (strategyState.waitingToBuy && currentPrice1 < strategyState.targetBuyPrice) {
        console.log(`Buy condition met for ${coin}, strategy ${strategyId}: ${currentPrice1} < ${strategyState.targetBuyPrice}`);
        await buy(contract0, sym0, strategy, currentPrice1);
        strategyState.waitingToBuy = false;
        return;
      }

      if (!strategyState.inTrade && strategyState.targetBuyPrice === 0) {
        console.log(`First-time buy triggered for ${coin}, strategy ${strategyId}...`);
        await buy(contract0, sym0, strategy, currentPrice1);
        return;
      }

      if (strategyState.inTrade && currentPrice1 >= strategyState.targetSellPrice) {
        console.log(`Sell condition met for ${coin}, strategy ${strategyId}: ${currentPrice1} >= ${strategyState.targetSellPrice}`);
        await sell(contract1, strategy, currentPrice0,currentPrice1);
        return;
      }
      // 检查是否满足加仓条件
      if (
        strategyState.inTrade &&
        strategyState.currentAdditions < strategyState.maxAdditions &&
        currentPrice1 <= strategyState.averageBuyPrice * 0.95
      ) {
        console.log(`Add condition met: ${currentPrice1} <= ${strategyState.averageBuyPrice * 0.95}`);
        strategyState.currentAdditions += 1; // 增加加仓次数
        await buy(contract0, sym0, strategy, currentPrice1);
        return;
      }
      // if (strategyState.inTrade) {   
      //   await sell(contract1, strategy, currentPrice);
      //   return;
      // }

    } catch (error) {
      console.error(`Error checking data or processing trade for ${coin}, strategy ${strategyId}:`, error);
    }
  };

  // 轮询每个策略
  setInterval(() => {
    strategies.forEach((strategy) => checkAndTrade(strategy));
  }, 8000);
};

// 从配置中加载策略
const strategies = config.strategies; // 假设配置文件中包含策略数组

// 筛选出 Enable 为 true 的策略
const enabledStrategies = strategies.filter(strategy => strategy.Enable === true);

logToFile(`Enabled strategies: ${enabledStrategies.map(strategy => strategy.strategyId).join(', ')}`);

startSwapProcess(enabledStrategies);