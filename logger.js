const fs = require('fs');
const path = require('path');
// const chalk = require('chalk');

const logFilePath = path.join(__dirname, 'logs', 'app.log');

// 确保日志文件夹存在
if (!fs.existsSync(path.dirname(logFilePath))) {
    fs.mkdirSync(path.dirname(logFilePath));
}

function logToFile(message, iswrite = true) {
    const timestamp = new Date().toLocaleString();
    
    // 检查 message 是否为对象
    if (typeof message === 'object') {
        try {
            // 将对象转换为 JSON 字符串
            message = JSON.stringify(message, null, 2); // 美化输出，缩进两个空格
        } catch (error) {
            message = '无法转换对象为字符串'; // 如果转换失败，返回提示信息
        }
    }

    if (iswrite) {
        fs.appendFileSync(logFilePath, `${timestamp} - ${message}\n`, 'utf8');
    }
    // console.log(`时间： ${timestamp} 消息： ${message}`);
      // 使用 chalk 设置颜色
    //   console.log(chalk.blue(`时间： ${timestamp} 消息： `) + chalk.green(message));
       // 使用 ANSI 转义码设置颜色
    console.log(`\x1b[34m时间： ${timestamp} 消息： \x1b[32m${message}\x1b[0m`); // 蓝色时间，绿色消息

}


// module.exports = logToFile; // 导出日志函数


module.exports = { logToFile };
