const { JsonRpc } = require('eosjs');
const fetch = (...args) =>
    import('node-fetch').then(({ default: fetch }) => fetch(...args));
const config = require('./config.json');
const { logToFile } = require('./logger');

// 设置固定的连接 URL
const RPC_URL = 'https://api.dfs.land/dfschain/rpcurls';

// 封装成函数
async function fetchRpcUrls() {
    try {
        const response = await fetch(RPC_URL);
        if (!response.ok) {
            throw new Error('网络响应不正确');
        }
        const data = await response.json();

        // 提取所有的 rpcurl
        const rpcUrls = data.map(item => item.rpcurl);
        return rpcUrls; // 返回 rpcurl 数组
    } catch (error) {
        console.error('获取数据时发生错误:', error);
        throw error; // 抛出错误以便于调用者处理
    }
}

// 超时工具函数
function withTimeout(promise, ms, errorMessage = '请求超时') {
    return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => reject(new Error(errorMessage)), ms);
        promise
            .then((result) => {
                clearTimeout(timeout);
                resolve(result);
            })
            .catch((error) => {
                clearTimeout(timeout);
                reject(error);
            });
    });
}

// 初始化 RPC 节点列表
async function initializeRpcs() {
    const rpcUrlsFromConfig = config.dfs_server_urls || []; // 从配置文件中读取
    let fetchedUrls = [];

    try {
        // 动态获取 RPC URLs
        fetchedUrls = await fetchRpcUrls();
        console.log('动态获取的 RPC URLs:', fetchedUrls);
    } catch (error) {
        console.error('动态获取 RPC URLs 出错:', error.message);
    }

    // 合并配置和动态获取的 URLs，并去重
    const rpcUrls = [...new Set([...rpcUrlsFromConfig, ...fetchedUrls])];
    // console.log('合并后的 RPC URLs:', rpcUrls);

    // 记录到日志文件
    // logToFile(`合并后的 RPC URLs: ${JSON.stringify(rpcUrls)}`);

    // 创建 RPC 实例列表
    const rpcs = rpcUrls.map((url) => new JsonRpc(url, { fetch }));
    return rpcs;
}

// 选出延迟最低的节点
async function selectOptimalRpc(timeout = 5000) {
    const rpcs = await initializeRpcs(); // 初始化所有 RPC 节点

    if (rpcs.length === 0) {
        throw new Error('没有可用的 RPC 节点');
    }

    const rpcPromises = rpcs.map(async (rpc) => {
        const start = Date.now(); // 记录开始时间
        try {
            const info = await withTimeout(rpc.get_info(), timeout, `节点 ${rpc.endpoint} 超时`);
            const latency = Date.now() - start; // 计算延迟
            // console.log(`节点 ${rpc.endpoint} 可用, 延迟: ${latency}ms, 信息: ${JSON.stringify(info)}`);
            return { rpc, latency }; // 返回节点和延迟信息
        } catch (error) {
            console.error(`节点 ${rpc.endpoint} 不可用: ${error.message}`);
            return null; // 返回 null 表示节点不可用
        }
    });

    // 并发检查所有节点，收集结果
    const results = await Promise.allSettled(rpcPromises);

    // 过滤出成功的节点
    const availableRpcs = results
        .filter((result) => result.status === 'fulfilled' && result.value !== null)
        .map((result) => result.value);

    if (availableRpcs.length === 0) {
        throw new Error('所有节点均不可用');
    }

    // 根据延迟排序，选出延迟最低的节点
    const optimalRpc = availableRpcs.sort((a, b) => a.latency - b.latency)[0];
    console.log(`选择的最优节点: ${optimalRpc.rpc.endpoint}, 延迟: ${optimalRpc.latency}ms`);

    // logToFile(`选择的最优节点: ${optimalRpc.rpc}, 延迟: ${optimalRpc.latency}ms`);

    // 返回 JsonRpc 对象
    return optimalRpc.rpc; // 确保这里返回的是 JsonRpc 实例
}

module.exports = { selectOptimalRpc };
